import { Modu<PERSON> } from '@nestjs/common';
import { PhotosService } from './photos.service';
import { PhotosController } from './photos.controller';
import { Photos, PhotosSchema } from './schemas/photos.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersModule } from 'src/users/users.module';
import { PhotosLike, PhotosLikeSchema } from './schemas/photos-like.schema';
import {
  PhotosFavorite,
  PhotosFavoriteSchema,
} from './schemas/photos-favorite.schema';
import { Users, UsersSchema } from 'src/users/schemas/users.schema';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Photos.name,
        useFactory: () => {
          const schema = PhotosSchema;
          schema.plugin(require('mongoose-autopopulate'));
          return schema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: Users.name, schema: UsersSchema },
      { name: Photos.name, schema: PhotosSchema },
      { name: PhotosLike.name, schema: PhotosLikeSchema },
      { name: PhotosFavorite.name, schema: PhotosFavoriteSchema },
    ]),
    UsersModule, // 导入 UsersModule
  ],
  controllers: [PhotosController],
  providers: [PhotosService],
})
export class PhotosModule {}
