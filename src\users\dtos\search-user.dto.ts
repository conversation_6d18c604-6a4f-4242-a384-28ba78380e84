import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, IsIn } from 'class-validator';

export class SearchUserDto {
  /** 当前页码（从1开始） */
  @ApiProperty({ description: '当前页码（从1开始）', example: 1 })
  @IsOptional()
  @IsNumber()
  page: number;

  /** 每页数据条数（默认10） */
  @ApiProperty({ description: '每页数据条数（默认10）', example: 10 })
  @IsOptional()
  @IsNumber()
  pageSize: number;

  /** 排序字段（需与后端字段名一致） */
  @ApiProperty({
    description: '排序字段（需与后端字段名一致）',
    example: 'createTime',
  })
  @IsOptional()
  @IsString()
  @IsIn([
    'createTime',
    'nickname',
    'uid',
    'updateTime',
    'followingsCount',
    'visitedCount',
    'totalDownloads',
  ]) // 允许的排序字段
  sortField: string;

  /** 排序方式：asc-升序 desc-降序 */
  @ApiProperty({
    description: '排序方式：asc-升序 desc-降序',
    example: 'asc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc']) // 允许的排序方式
  sortOrder: 'asc' | 'desc';

  /** 搜索关键字 */
  @ApiProperty({ description: '搜索关键字', example: '六花' })
  @IsOptional()
  @IsString()
  keyword: string;
}
