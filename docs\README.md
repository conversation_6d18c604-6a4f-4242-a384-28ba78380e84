# WebSocket 实时通信系统文档

## 📚 文档导航

欢迎使用我们的WebSocket实时通信系统！这里是完整的文档索引，帮助您快速找到需要的信息。

### 📖 核心功能文档 (按功能分类)

1. **[连接与认证](./01-connection-auth.md)** - WebSocket连接建立、JWT认证、重连机制
2. **[消息传递功能](./02-messaging.md)** - 私聊、群聊、系统通知的完整实现
3. **[用户状态管理](./03-user-status.md)** - 在线状态、状态更新、用户查询功能
4. **[群组管理](./04-group-management.md)** - 群组加入/离开、成员管理、群聊功能
5. **[错误处理](./05-error-handling.md)** - 错误类型、恢复机制、监控预防

### 🚀 快速开始

- **[快速开始](./quick-start.md)** - 3分钟快速上手指南

## 🌟 系统特性

### ✅ 核心功能

- **实时消息传递** - 支持私聊、群聊、系统通知
- **用户在线状态** - 实时跟踪用户在线/离线/忙碌状态
- **群组管理** - 动态加入/离开聊天群组
- **JWT认证** - 安全的用户身份验证
- **跨服务器支持** - 基于Redis的分布式架构

### 🔧 技术特性

- **高性能** - 内存+Redis双重缓存策略
- **高可用** - 自动重连和错误恢复
- **可扩展** - 模块化设计，易于扩展新功能
- **类型安全** - 完整的TypeScript类型定义

## 🚀 快速开始

### 1. 前端连接

```javascript
import { io } from 'socket.io-client';

const socket = io('ws://localhost:3000/ws', {
  auth: { token: 'Bearer your-jwt-token' },
});

socket.on('connect', () => {
  console.log('连接成功！');
});
```

### 2. 监听事件

```javascript
// 监听用户上线
socket.on('user_online', (data) => {
  console.log('用户上线:', data.user.nickname);
});

// 监听私聊消息
socket.on('private_message', (data) => {
  console.log('收到消息:', data.content);
});
```

### 3. 发送消息

```javascript
// 发送私聊消息
socket.emit('private_message', {
  receiverId: 'user123',
  content: '你好！',
});

// 更新状态
socket.emit('update_user_status', { status: 'away' });
```

## 📡 主要事件

### 客户端发送事件

| 事件名                 | 数据格式                | 说明             |
| ---------------------- | ----------------------- | ---------------- |
| `private_message`      | `{receiverId, content}` | 发送私聊消息     |
| `group_message`        | `{groupId, content}`    | 发送群聊消息     |
| `update_user_status`   | `{status}`              | 更新用户状态     |
| `get_all_online_users` | -                       | 获取在线用户列表 |
| `join_group`           | `groupId`               | 加入群组         |
| `leave_group`          | `groupId`               | 离开群组         |

### 服务端推送事件

| 事件名               | 数据格式                              | 说明         |
| -------------------- | ------------------------------------- | ------------ |
| `user_online`        | `{user, timestamp}`                   | 用户上线通知 |
| `user_offline`       | `{userId, lastSeen, timestamp}`       | 用户下线通知 |
| `private_message`    | `{from, content, timestamp}`          | 接收私聊消息 |
| `group_message`      | `{groupId, from, content, timestamp}` | 接收群聊消息 |
| `user_status_update` | `{user, timestamp}`                   | 用户状态更新 |

## 🏗️ 系统架构

```
客户端 → WebSocket网关 → Redis/MongoDB
         (NestJS)      (数据存储)
```

**核心流程**: 连接认证 → 消息路由 → 状态管理 → 实时广播

## 🔐 安全机制

### 认证安全

- **JWT Token验证** - 每个连接都需要有效的JWT令牌
- **权限控制** - 不同操作需要相应的用户权限
- **自动断开** - 未认证或异常连接自动断开

### 数据安全

- **输入验证** - 所有输入数据都经过DTO验证
- **内容过滤** - 自动过滤恶意内容和敏感词汇
- **CORS配置** - 跨域请求安全控制

## 📈 性能优化

### 存储策略

- **内存缓存** - 用户-Socket映射，毫秒级访问
- **Redis缓存** - 状态持久化，支持过期时间
- **数据库** - 用户基本信息，按需查询

### 网络优化

- **事件压缩** - 最小化传输数据
- **批量操作** - 减少Redis调用次数
- **连接复用** - 高效的连接管理

## 🛠️ 开发指南

### 添加新事件

1. 在DTO中定义数据结构
2. 在Gateway中添加事件处理器
3. 更新文档和类型定义

```typescript
// 1. 定义DTO
export class NewEventDto {
  data: string;
}

// 2. 添加处理器
@SubscribeMessage('new_event')
async handleNewEvent(
  @MessageBody() data: NewEventDto,
  @ConnectedSocket() client: Socket
) {
  // 处理逻辑
}
```

### 调试技巧

```javascript
// 开启调试模式
localStorage.debug = 'socket.io-client:socket';

// 监听所有事件
socket.onAny((event, ...args) => {
  console.log(`事件: ${event}`, args);
});
```

## 🔧 配置说明

### 环境变量

```env
# WebSocket配置
WS_PORT=3000
WS_NAMESPACE=/ws

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT配置
JWT_SECRET=your-secret-key
```

### 生产环境建议

- 启用HTTPS/WSS
- 配置负载均衡
- 设置连接限制
- 启用监控和日志

## 🐛 常见问题

### 连接问题

**Q: 连接失败怎么办？**
A: 检查JWT Token是否有效，确认服务器地址正确

**Q: 频繁断线重连？**
A: 实现重连机制，检查网络稳定性

### 消息问题

**Q: 消息发送失败？**
A: 确认接收者ID有效，检查用户权限

**Q: 收不到消息？**
A: 确认事件监听器正确设置，检查连接状态

### 性能问题

**Q: 连接数过多导致性能下降？**
A: 考虑使用负载均衡，优化Redis配置

## 📞 技术支持

- **文档问题** - 查看相关文档或提交Issue
- **Bug报告** - 提供详细的错误信息和复现步骤
- **功能建议** - 欢迎提出改进建议

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 LICENSE 文件。

---

**开始使用**: 建议从 [使用指南](./websocket-usage-guide.md) 开始，然后根据需要查看其他文档。

**需要帮助**: 如果遇到问题，请先查看 [常见问题](#-常见问题) 部分，或联系开发团队。

