# WebSocket 用户在线状态管理文档

## 🌟 概述

本文档详细介绍了WebSocket模块中的用户在线状态管理功能，包括用户上线/下线检测、状态更新、在线用户查询等功能。

## 🔧 核心功能

### 1. 自动状态管理
- ✅ 用户连接时自动设置为在线状态
- ✅ 用户断开时自动设置为离线状态
- ✅ 记录用户最后活跃时间
- ✅ 存储用户设备信息

### 2. 状态类型
- `online` - 在线
- `away` - 离开
- `busy` - 忙碌
- `offline` - 离线

### 3. 数据存储
- **内存存储**: 快速访问的用户-Socket映射
- **Redis存储**: 持久化的用户状态信息

## 📡 WebSocket 事件

### 客户端发送事件

| 事件名 | 数据格式 | 说明 | 权限要求 |
|--------|----------|------|----------|
| `update_user_status` | `{status: string}` | 更新用户状态 | 已认证用户 |
| `get_user_status` | `{userId: string}` | 获取指定用户状态 | 已认证用户 |
| `get_batch_user_status` | `{userIds: string[]}` | 批量获取用户状态 | 已认证用户 |
| `get_all_online_users` | - | 获取所有在线用户 | 已认证用户 |

### 服务端推送事件

| 事件名 | 数据格式 | 说明 |
|--------|----------|------|
| `user_online` | `{user, timestamp}` | 用户上线通知 |
| `user_offline` | `{userId, lastSeen, timestamp}` | 用户下线通知 |
| `user_status_update` | `{user, timestamp}` | 用户状态更新通知 |
| `status_update_response` | `{success, status, timestamp}` | 状态更新响应 |
| `user_status_response` | `{success, data, timestamp}` | 用户状态查询响应 |
| `batch_user_status_response` | `{success, data, timestamp}` | 批量状态查询响应 |
| `all_online_users_response` | `{success, data, timestamp}` | 在线用户列表响应 |

## 🚀 使用示例

### 基础连接和状态监听

```javascript
import { io } from 'socket.io-client';

const socket = io('ws://localhost:3000/ws', {
  auth: { token: 'Bearer your-jwt-token' }
});

// 监听用户上线事件
socket.on('user_online', (data) => {
  console.log('用户上线:', data.user);
  // data.user: { id, nickname, avatar, status, lastSeen }
});

// 监听用户下线事件
socket.on('user_offline', (data) => {
  console.log('用户下线:', data.userId, '最后活跃:', data.lastSeen);
});

// 监听用户状态更新
socket.on('user_status_update', (data) => {
  console.log('用户状态更新:', data.user);
  // 更新UI中的用户状态显示
});
```

### 更新用户状态

```javascript
// 更新自己的状态
function updateMyStatus(status) {
  socket.emit('update_user_status', { status });
}

// 监听状态更新响应
socket.on('status_update_response', (response) => {
  if (response.success) {
    console.log('状态更新成功:', response.status);
  } else {
    console.error('状态更新失败:', response.error);
  }
});

// 使用示例
updateMyStatus('away'); // 设置为离开状态
updateMyStatus('busy'); // 设置为忙碌状态
updateMyStatus('online'); // 设置为在线状态
```

### 查询用户状态

```javascript
// 查询单个用户状态
function getUserStatus(userId) {
  socket.emit('get_user_status', { userId });
}

// 监听用户状态响应
socket.on('user_status_response', (response) => {
  if (response.success) {
    const { userId, status, lastSeen, device, isOnline } = response.data;
    console.log(`用户 ${userId} 状态:`, {
      status,
      lastSeen,
      device,
      isOnline
    });
  } else {
    console.error('获取用户状态失败:', response.error);
  }
});

// 批量查询用户状态
function getBatchUserStatus(userIds) {
  socket.emit('get_batch_user_status', { userIds });
}

// 监听批量状态响应
socket.on('batch_user_status_response', (response) => {
  if (response.success) {
    response.data.forEach(userStatus => {
      console.log(`用户 ${userStatus.userId}:`, userStatus);
    });
  } else {
    console.error('批量获取用户状态失败:', response.error);
  }
});
```

### 获取所有在线用户

```javascript
// 获取所有在线用户
function getAllOnlineUsers() {
  socket.emit('get_all_online_users');
}

// 监听在线用户列表响应
socket.on('all_online_users_response', (response) => {
  if (response.success) {
    const { count, users } = response.data;
    console.log(`当前在线用户数: ${count}`);
    users.forEach(user => {
      console.log('在线用户:', user);
      // user: { id, nickname, avatar, status, lastSeen }
    });
  } else {
    console.error('获取在线用户失败:', response.error);
  }
});
```

## 🎯 完整的用户状态管理组件

### React Hook 示例

```javascript
import { useEffect, useState } from 'react';
import { io } from 'socket.io-client';

export const useUserStatus = (token) => {
  const [socket, setSocket] = useState(null);
  const [onlineUsers, setOnlineUsers] = useState([]);
  const [userStatuses, setUserStatuses] = useState(new Map());
  const [myStatus, setMyStatus] = useState('online');

  useEffect(() => {
    if (!token) return;

    const newSocket = io('ws://localhost:3000/ws', {
      auth: { token: `Bearer ${token}` }
    });

    // 监听用户上线
    newSocket.on('user_online', (data) => {
      setOnlineUsers(prev => {
        const filtered = prev.filter(u => u.id !== data.user.id);
        return [...filtered, data.user];
      });
      setUserStatuses(prev => new Map(prev.set(data.user.id, data.user)));
    });

    // 监听用户下线
    newSocket.on('user_offline', (data) => {
      setOnlineUsers(prev => prev.filter(u => u.id !== data.userId));
      setUserStatuses(prev => {
        const newMap = new Map(prev);
        const user = newMap.get(data.userId);
        if (user) {
          newMap.set(data.userId, { ...user, status: 'offline', lastSeen: data.lastSeen });
        }
        return newMap;
      });
    });

    // 监听状态更新
    newSocket.on('user_status_update', (data) => {
      setUserStatuses(prev => new Map(prev.set(data.user.id, data.user)));
      setOnlineUsers(prev => 
        prev.map(u => u.id === data.user.id ? data.user : u)
      );
    });

    // 监听状态更新响应
    newSocket.on('status_update_response', (response) => {
      if (response.success) {
        setMyStatus(response.status);
      }
    });

    // 获取初始在线用户列表
    newSocket.emit('get_all_online_users');
    newSocket.on('all_online_users_response', (response) => {
      if (response.success) {
        setOnlineUsers(response.data.users);
        const statusMap = new Map();
        response.data.users.forEach(user => {
          statusMap.set(user.id, user);
        });
        setUserStatuses(statusMap);
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [token]);

  const updateStatus = (status) => {
    if (socket) {
      socket.emit('update_user_status', { status });
    }
  };

  const getUserStatus = (userId) => {
    if (socket) {
      socket.emit('get_user_status', { userId });
    }
  };

  const refreshOnlineUsers = () => {
    if (socket) {
      socket.emit('get_all_online_users');
    }
  };

  return {
    socket,
    onlineUsers,
    userStatuses,
    myStatus,
    updateStatus,
    getUserStatus,
    refreshOnlineUsers
  };
};
```

### 使用Hook的组件示例

```javascript
function UserStatusComponent() {
  const { 
    onlineUsers, 
    userStatuses, 
    myStatus, 
    updateStatus, 
    refreshOnlineUsers 
  } = useUserStatus(userToken);

  return (
    <div>
      <div>
        <h3>我的状态: {myStatus}</h3>
        <button onClick={() => updateStatus('online')}>在线</button>
        <button onClick={() => updateStatus('away')}>离开</button>
        <button onClick={() => updateStatus('busy')}>忙碌</button>
      </div>

      <div>
        <h3>在线用户 ({onlineUsers.length})</h3>
        <button onClick={refreshOnlineUsers}>刷新</button>
        {onlineUsers.map(user => (
          <div key={user.id} className="user-item">
            <img src={user.avatar} alt={user.nickname} />
            <span>{user.nickname}</span>
            <span className={`status ${user.status}`}>{user.status}</span>
            <span>最后活跃: {new Date(user.lastSeen).toLocaleString()}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
```

## 📊 数据结构

### 用户状态对象
```typescript
interface UserStatus {
  id: string;           // 用户ID
  nickname: string;     // 用户昵称
  avatar: string;       // 用户头像
  status: 'online' | 'away' | 'busy' | 'offline'; // 状态
  lastSeen: Date;       // 最后活跃时间
  device?: string;      // 设备信息
  isOnline: boolean;    // 是否在线
}
```

### Redis 存储键值

| 键名 | 类型 | 说明 | 过期时间 |
|------|------|------|----------|
| `user:{userId}:online` | String | 用户在线标记 | 1小时 |
| `user:{userId}:socket` | String | Socket ID | 1小时 |
| `user:{userId}:status` | String | 用户状态 | 1小时 |
| `user:{userId}:last_seen` | String | 最后活跃时间 | 24小时 |
| `user:{userId}:device` | String | 设备信息 | 1小时 |
| `online_users` | Hash | 在线用户映射 | 无过期 |

## ⚠️ 注意事项

1. **状态同步**: 用户状态会在所有连接的客户端之间实时同步
2. **自动清理**: Redis中的状态信息会自动过期，避免内存泄漏
3. **设备检测**: 系统会自动记录用户的设备信息
4. **权限控制**: 所有状态相关操作都需要用户认证
5. **性能考虑**: 批量查询用户状态时建议限制用户数量

## 🔗 相关文档

- [WebSocket 完整使用文档](./websocket-usage.md)
- [Socket.IO 官方文档](https://socket.io/docs/)
