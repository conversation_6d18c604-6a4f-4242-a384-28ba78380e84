import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({
  // 禁用 __v 版本字段
  versionKey: false,
})
export class ContentsLike {
  @Prop({
    type: String,
    ref: 'Users',
    required: true,
    index: true,
  })
  user: string;

  @Prop({
    type: String,
    ref: 'Contents',
    required: true,
    index: true,
  })
  content: string;

  /** 点赞时间 */
  @Prop({ default: Date.now })
  createTime: Date;
}

export const ContentsLikeSchema = SchemaFactory.createForClass(ContentsLike);
