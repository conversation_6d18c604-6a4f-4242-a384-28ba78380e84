import { ApiProperty } from '@nestjs/swagger';
import { IsString, Length, IsNotEmpty, IsPhoneNumber } from 'class-validator';

export class sendVerificationPhoneDto {
  @ApiProperty({
    description: '手机号',
    required: true,
    example: '13021322222',
  })
  @IsPhoneNumber('CN', { message: '手机号格式不正确' }) // 确保字段是手机号格式
  @IsNotEmpty({ message: '手机号不能为空' }) // 字段不能为空
  readonly phone: string;

  @ApiProperty({
    description: '图像验证码',
    required: true,
    example: '12ab',
  })
  @IsString({ message: '验证码必须是字符串类型' }) // 确保字段是字符串类型
  @Length(4, 4, { message: '验证码长度必须为4位' }) // 确保字段长度为4位
  @IsNotEmpty({ message: '验证码不能为空' }) // 字段不能为空
  readonly code: string;
}
