import { Expose, Type } from 'class-transformer';
import { PhotoStatusEnum } from 'src/shared/enums/Photo.enum';

class PhotoAttributesDto {
  @Expose()
  size: number; // 图片大小
  @Expose()
  width: number; // 图片宽度
  @Expose()
  height: number; // 图片高度
  @Expose()
  format: string; // 图片格式
}

export class PhotoProfileInfoDto {
  @Expose()
  id: string; // 图片ID
  @Expose()
  filename: string; // 图片名称
  @Expose()
  url: string; // 图片地址
  @Expose()
  category: string; // 分类
  @Expose()
  tags: Array<string>; // 标签
  @Expose()
  favoriteCount: number; // 收藏量
  @Expose()
  likeCount: number; // 点赞量
  @Expose()
  isLiked: boolean; // 是否点赞
  @Expose()
  isFavorited: boolean; // 是否收藏
  @Expose()
  viewCount: number; // 浏览量
  @Expose()
  downloadCount: number; // 下载次数
  @Expose()
  @Type(() => PhotoAttributesDto)
  attributes: PhotoAttributesDto; // 图片属性
  @Expose()
  status: PhotoStatusEnum; // 状态
}
