// @ts-check
// 启用TypeScript检查，确保代码符合TypeScript的类型规则
import eslint from '@eslint/js';
// 导入ESLint的默认配置
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';
// 导入eslint-plugin-prettier插件的推荐配置，该插件用于集成Prettier和ESLint，以确保代码风格一致
import globals from 'globals';
// 导入globals模块，该模块提供了一些常见的全局变量定义，用于在ESLint中配置全局变量
import tseslint from 'typescript-eslint';
// 导入typescript-eslint模块，该模块提供了用于检查TypeScript代码的规则和配置
export default tseslint.config(
  // 导出一个默认的ESLint配置对象，该对象将被用于项目的ESLint检查
  {
    ignores: ['eslint.config.mjs', 'webpack-hmr.config.js'],
    // 配置ESLint忽略检查的文件列表，这里忽略了当前的配置文件本身
  },
  eslint.configs.recommended,
  // 应用ESLint的推荐配置规则
  ...tseslint.configs.recommendedTypeChecked,
  // 应用typescript-eslint的推荐配置规则，这些规则依赖于TypeScript的类型检查功能
  eslintPluginPrettierRecommended,
  // 应用eslint-plugin-prettier的推荐配置规则，以确保代码风格符合Prettier的设置
  {
    languageOptions: {
      // 配置ESLint的语言选项，用于指定代码的全局变量、源类型和解析器选项
      globals: {
        // 定义项目中使用的全局变量，这里包含了Node.js和Jest环境中的全局变量
        ...globals.node,
        ...globals.jest,
      },
      sourceType: 'commonjs',
      // 设置源代码类型为CommonJS，意味着代码将使用require和module.exports而不是ES6的import/export
      parserOptions: {
        // 配置解析器选项，这里指定了使用TypeScript项目服务，并设置了tsconfig文件的根目录
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },
  {
    rules: {
      '@typescript-eslint/no-explicit-any': 'off', // 关闭禁止使用 'any' 类型的检查
      '@typescript-eslint/no-floating-promises': 'off', // 关闭禁止不处理的 Promise 的检查
      '@typescript-eslint/no-unsafe-argument': 'warn', // 将不安全的参数传递给函数的检查设为警告
      '@typescript-eslint/ban-ts-comment': 'off', // 关闭禁止使用特定 ts 注释的检查
      '@typescript-eslint/no-unsafe-return': 'off', // 关闭禁止返回不安全类型的检查
      '@typescript-eslint/no-unsafe-assignment': 'warn', // 将不安全的类型赋值检查设为警告
      '@typescript-eslint/no-unsafe-call': 'warn', // 将不安全的函数调用检查设为警告
      '@typescript-eslint/no-unsafe-member-access': 'warn', // 将不安全的成员访问检查设为警告
      '@typescript-eslint/no-unused-vars': 'warn', // 将未使用的变量检查设为警告
      '@typescript-eslint/no-require-imports': 'off', // 关闭禁止使用 require 导入的检查
    },
  }
);
