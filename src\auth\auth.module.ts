import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core'; // 提供全局守卫的令牌
import { JwtModule } from '@nestjs/jwt'; // 引入 JwtModule 用于 JWT 处理
import { AuthController } from './auth.controller'; // 引入 AuthController
import { AuthGuard } from '../common/guard/auth.guard'; // 引入 AuthGuard
import { AuthService } from './auth.service'; // 引入 AuthService
import * as config from 'config'; // 引入配置模块
import { RedisModule } from 'src/redis/redis.module';
import { UsersModule } from 'src/users/users.module';
import { CaptchaModule } from 'src/captcha/captcha.module';

const jwtConfig = config.get<JwtConfig>('jwt'); // 获取 JWT 相关配置

@Module({
  imports: [
    UsersModule, // 导入 UserModule，相互依赖的模块需使用 forwardRef
    RedisModule, // 导入 RedisModule，用于 Redis 相关的操作
    CaptchaModule, // 导入 CaptchaModule，用于验证码相关的操作
    JwtModule.register({
      global: true, // 全局注册 JwtModule
      secret: jwtConfig.secret, // 使用配置文件中的密钥进行 JWT 加密
      signOptions: { expiresIn: jwtConfig.expiresIn }, // 设置 JWT 令牌的过期时间
    }),
  ],
  providers: [
    AuthService, // 提供 AuthService
    {
      provide: APP_GUARD, // 提供全局守卫
      useClass: AuthGuard, // 使用 AuthGuard 作为全局守卫
    },
  ],
  controllers: [AuthController], // 提供 AuthController
  exports: [AuthService], // 导出 AuthService，以便其他模块可以使用
})
export class AuthModule {} // 导出 AuthModule
