import { IsArray, IsIn, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/** 获取图片的查询条件 */
export class SearchAllPhotoDto {
  /** 当前页码（从1开始） */
  @ApiProperty({ description: '当前页码（从1开始）', example: 1 })
  @IsOptional()
  @IsNumber()
  page: number;

  /** 每页数据条数（默认10） */
  @ApiProperty({ description: '每页数据条数（默认10）', example: 10 })
  @IsOptional()
  @IsNumber()
  pageSize: number;

  /** 排序字段（需与后端字段名一致） */
  @ApiProperty({
    description: '排序字段（需与后端字段名一致）',
    example: 'createTime',
  })
  @IsOptional()
  @IsString()
  @IsIn([
    'createTime',
    'updateTime',
    'downloadCount',
    'viewCount',
    'likeCount',
    'favoriteCount',
  ]) // 允许的排序字段
  sortField: string;

  /** 排序方式：asc-升序 desc-降序 */
  @ApiProperty({
    description: '排序方式：asc-升序 desc-降序',
    example: 'asc',
  })
  @IsOptional()
  @IsIn(['asc', 'desc']) // 允许的排序方式
  sortOrder: 'asc' | 'desc';

  /** 分类 */
  @ApiProperty({ description: '图片分类' })
  @IsOptional()
  @IsString({ message: '分类必须是字符串' })
  category: string;

  /** 标签 */
  @ApiProperty({ description: '图片标签' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true, message: '标签必须是字符串数组' })
  tags: string[];

  /** 关键词 */
  @ApiProperty({ description: '关键词' })
  @IsOptional()
  @IsString({ message: '关键词必须是字符串' })
  keyword: string;
}
