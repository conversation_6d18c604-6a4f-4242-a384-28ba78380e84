import { Module } from '@nestjs/common';
import { ContentsService } from './contents.service';
import { ContentsController } from './contents.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Contents, ContentsSchema } from './schemas/contents.schema';
import { UsersModule } from 'src/users/users.module';
import {
  ContentsLike,
  ContentsLikeSchema,
} from './schemas/contents-like.schema';
import {
  ContentsFavorite,
  ContentsFavoriteSchema,
} from './schemas/contents-favorite.schema';
import { Users, UsersSchema } from 'src/users/schemas/users.schema';
import { Photos, PhotosSchema } from 'src/photos/schemas/photos.schema';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: Contents.name,
        useFactory: () => {
          const schema = ContentsSchema;
          schema.plugin(require('mongoose-autopopulate'));
          return schema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: Users.name, schema: UsersSchema },
      { name: Photos.name, schema: PhotosSchema },
      { name: Contents.name, schema: ContentsSchema },
      { name: ContentsLike.name, schema: ContentsLikeSchema },
      { name: ContentsFavorite.name, schema: ContentsFavoriteSchema },
    ]),
    UsersModule,
  ],
  controllers: [ContentsController],
  providers: [ContentsService],
})
export class ContentsModule {}
