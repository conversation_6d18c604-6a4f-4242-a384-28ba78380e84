# WebSocket 系统架构详解

## 🏗️ 系统概述

本WebSocket系统是一个基于NestJS和Socket.IO构建的实时通信解决方案，支持用户在线状态管理、实时消息传递、群组聊天等功能。

## 📊 系统架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        客户端层 (Frontend)                        │
├─────────────────────────────────────────────────────────────────┤
│  React/Vue/Angular 应用                                          │
│  ├── Socket.IO Client                                           │
│  ├── JWT Token 认证                                             │
│  └── 事件监听和发送                                               │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    │ WebSocket 连接
                                    │ ws://localhost:3000/ws
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      WebSocket 网关层                            │
├─────────────────────────────────────────────────────────────────┤
│  WebsocketGateway (src/websocket/websocket.gateway.ts)          │
│  ├── 连接管理 (handleConnection/handleDisconnect)               │
│  ├── 消息路由 (@SubscribeMessage 装饰器)                        │
│  ├── 用户认证 (JWT Token 验证)                                   │
│  ├── 事件广播 (server.emit)                                     │
│  └── 错误处理和日志记录                                          │
└─────────────────────────────────────────────────────────────────┘
                                    │
                    ┌───────────────┼───────────────┐
                    │               │               │
                    ▼               ▼               ▼
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│   内存存储层     │ │   Redis存储层    │ │   数据库层       │
├─────────────────┤ ├─────────────────┤ ├─────────────────┤
│ Map<userId,     │ │ Redis Keys:     │ │ MongoDB:        │
│ socketId>       │ │                 │ │                 │
│                 │ │ • online_users  │ │ • users 集合    │
│ 用途：          │ │ • user:*:online │ │ • 用户基本信息   │
│ • 快速查找      │ │ • user:*:status │ │ • 头像、昵称等   │
│ • 临时缓存      │ │ • user:*:socket │ │                 │
│                 │ │ • user:*:device │ │ 用途：          │
│                 │ │                 │ │ • 持久化数据    │
│                 │ │ 用途：          │ │ • 用户信息查询   │
│                 │ │ • 状态持久化    │ │                 │
│                 │ │ • 跨服务器共享   │ │                 │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 🔧 核心组件详解

### 1. WebsocketGateway (主要组件)

**文件位置**: `src/websocket/websocket.gateway.ts`

**主要职责**:
- 处理WebSocket连接的建立和断开
- 管理用户在线状态
- 路由和处理各种消息事件
- 广播消息给客户端

**关键方法**:
```typescript
// 连接处理
async handleConnection(client: Socket)

// 断开处理  
async handleDisconnect(client: Socket)

// 消息事件处理
@SubscribeMessage('event_name')
async handleEvent(data: any, client: Socket)
```

### 2. 认证系统 (WsAuthGuard)

**文件位置**: `src/websocket/ws-auth.guard.ts`

**认证流程**:
1. 客户端连接时传递JWT Token
2. 服务端验证Token有效性
3. 提取用户信息存储到Socket上下文
4. 未认证连接自动断开

**使用方式**:
```javascript
// 客户端连接示例
const socket = io('ws://localhost:3000/ws', {
  auth: {
    token: 'Bearer your-jwt-token'
  }
});
```

### 3. 数据存储策略

#### 内存存储 (Map)
```typescript
private onlineUsers = new Map<string, string>(); // userId -> socketId
```
- **优点**: 访问速度极快
- **缺点**: 重启后丢失，无法跨服务器共享
- **用途**: 快速查找用户对应的Socket连接

#### Redis存储
```typescript
// 哈希表：存储所有在线用户
await this.redisClient.hSet('online_users', userId, socketId);

// 键值对：存储用户详细状态
await dataClient.set(`user:${userId}:online`, 'true', { EX: 3600 });
await dataClient.set(`user:${userId}:status`, 'online', { EX: 3600 });
await dataClient.set(`user:${userId}:last_seen`, timestamp, { EX: 86400 });
```
- **优点**: 持久化，支持过期时间，可跨服务器共享
- **缺点**: 网络延迟，相对较慢
- **用途**: 状态持久化，跨服务器实例数据共享

#### MongoDB存储
```typescript
const user = await this.userModel
  .findById(userId)
  .select('_id nickname avatar')
  .lean();
```
- **优点**: 持久化，结构化数据，支持复杂查询
- **缺点**: 访问速度最慢
- **用途**: 用户基本信息查询，持久化业务数据

## 🔄 数据流程图

### 用户连接流程
```
客户端发起连接
       │
       ▼
JWT Token验证
       │
   ┌───┴───┐
   │ 成功  │ 失败 → 断开连接
   ▼       │
存储到内存Map
   │
   ▼
存储到Redis
   │
   ▼
查询用户信息
   │
   ▼
广播上线事件
   │
   ▼
连接建立完成
```

### 消息处理流程
```
客户端发送消息
       │
       ▼
@SubscribeMessage装饰器路由
       │
       ▼
验证用户权限
       │
   ┌───┴───┐
   │ 有权限 │ 无权限 → 返回错误
   ▼       │
处理业务逻辑
   │
   ▼
更新状态(可选)
   │
   ▼
广播给目标用户
   │
   ▼
返回响应给发送者
```

## 📡 事件系统

### 事件分类

#### 1. 系统级事件 (自动触发)
- `user_online` - 用户上线 (服务端广播)
- `user_offline` - 用户下线 (服务端广播)
- `connect` - 连接成功 (Socket.IO内置)
- `disconnect` - 连接断开 (Socket.IO内置)

#### 2. 用户级事件 (客户端触发)
- `update_user_status` - 更新用户状态
- `get_user_status` - 获取用户状态
- `get_all_online_users` - 获取在线用户列表

#### 3. 消息级事件
- `private_message` - 私聊消息
- `group_message` - 群聊消息
- `system_notice` - 系统通知

### 事件处理模式

```typescript
// 1. 监听客户端事件
@SubscribeMessage('event_name')
async handleEvent(
  @MessageBody() data: EventDto,
  @ConnectedSocket() client: Socket
) {
  // 处理逻辑
}

// 2. 向客户端发送事件
// 发送给特定客户端
client.emit('response_event', data);

// 广播给所有客户端
this.server.emit('broadcast_event', data);

// 发送给特定Socket ID
this.server.to(socketId).emit('target_event', data);
```

## 🔐 安全机制

### 1. 认证安全
- JWT Token验证
- Token过期检查
- 用户权限验证

### 2. 数据安全
- 输入数据验证 (DTO)
- SQL注入防护
- XSS攻击防护

### 3. 连接安全
- CORS配置
- 连接频率限制
- 异常连接自动断开

## 📈 性能优化

### 1. 内存优化
- 使用Map进行快速查找
- 及时清理断开连接的数据
- Redis键值自动过期

### 2. 网络优化
- 事件数据最小化
- 批量操作减少Redis调用
- 使用lean()查询优化数据库访问

### 3. 并发优化
- 异步处理所有I/O操作
- 错误隔离防止连锁故障
- 连接池管理

## 🚀 扩展性设计

### 1. 水平扩展
- Redis作为共享状态存储
- 支持多个服务器实例
- 负载均衡支持

### 2. 功能扩展
- 模块化事件处理
- 插件式消息类型
- 可配置的广播策略

### 3. 监控扩展
- 详细的日志记录
- 性能指标收集
- 错误追踪和报警

## 🔧 配置说明

### WebSocket配置
```typescript
@WebSocketGateway({
  namespace: '/ws',        // WebSocket命名空间
  cors: { origin: '*' },   // CORS配置
})
```

### Redis配置
- 连接池配置
- 过期时间设置
- 发布订阅配置

### 数据库配置
- MongoDB连接
- 查询优化
- 索引配置

这个架构设计确保了系统的高性能、高可用性和良好的扩展性，为实时通信应用提供了坚实的基础。
