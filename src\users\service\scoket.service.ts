import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Users } from '../schemas/users.schema';
import { RedisService } from 'src/redis/redis.service';

export interface OnlineUser {
  id: string;
  nickname: string;
  avatar: string;
  lastSeen: Date;
  status: 'online' | 'away' | 'busy' | 'offline';
  socketId?: string;
}

export interface UserPresence {
  userId: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: Date;
  device?: string;
  location?: string;
}

@Injectable()
export class SocketService {
  private readonly logger = new Logger(SocketService.name);

  constructor(
    @InjectModel(Users.name) private readonly userModel: Model<Users>,
    private readonly redisService: RedisService
  ) {}

  /**
   * 用户上线处理
   */
  async handleUserOnline(
    userId: string,
    socketId: string,
    device?: string
  ): Promise<void> {
    try {
      // 更新Redis中的在线状态
      await this.redisService.set(`user:${userId}:online`, 'true', 3600); // 1小时过期
      await this.redisService.set(`user:${userId}:socket`, socketId, 3600);
      await this.redisService.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        86400
      ); // 24小时过期

      if (device) {
        await this.redisService.set(`user:${userId}:device`, device, 3600);
      }

      // 获取用户信息
      const user = await this.userModel
        .findById(userId)
        .select('_id nickname avatar')
        .lean();

      if (user) {
        // 通过Redis发布用户上线事件
        await this.redisService.publish(
          'user_online',
          JSON.stringify({
            user: {
              id: user._id.toString(),
              nickname: user.nickname,
              avatar: user.avatar,
              status: 'online',
              lastSeen: new Date(),
            },
            timestamp: Date.now(),
          })
        );

        this.logger.log(`用户 ${userId} 上线`);
      }
    } catch (error) {
      this.logger.error(`处理用户上线失败: ${(error as Error).message}`);
    }
  }

  /**
   * 用户下线处理
   */
  async handleUserOffline(userId: string): Promise<void> {
    try {
      // 更新Redis中的离线状态
      await this.redisService.del(`user:${userId}:online`);
      await this.redisService.del(`user:${userId}:socket`);
      await this.redisService.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        86400
      );

      // 通过Redis发布用户下线事件
      await this.redisService.publish(
        'user_offline',
        JSON.stringify({
          userId,
          lastSeen: new Date(),
          timestamp: Date.now(),
        })
      );

      this.logger.log(`用户 ${userId} 下线`);
    } catch (error) {
      this.logger.error(`处理用户下线失败: ${(error as Error).message}`);
    }
  }

  /**
   * 更新用户状态
   */
  async updateUserStatus(
    userId: string,
    status: 'online' | 'away' | 'busy' | 'offline'
  ): Promise<void> {
    try {
      await this.redisService.set(`user:${userId}:status`, status, 3600);
      await this.redisService.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        86400
      );

      // 获取用户信息
      const user = await this.userModel
        .findById(userId)
        .select('_id nickname avatar')
        .lean();

      if (user) {
        // 通过Redis发布状态更新事件
        await this.redisService.publish(
          'user_status_update',
          JSON.stringify({
            user: {
              id: user._id.toString(),
              nickname: user.nickname,
              avatar: user.avatar,
              status,
              lastSeen: new Date(),
            },
            timestamp: Date.now(),
          })
        );

        this.logger.log(`用户 ${userId} 状态更新为 ${status}`);
      }
    } catch (error) {
      this.logger.error(`更新用户状态失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取用户在线状态
   */
  async getUserOnlineStatus(userId: string): Promise<UserPresence | null> {
    try {
      const isOnline = await this.redisService.get(`user:${userId}:online`);
      const status =
        (await this.redisService.get(`user:${userId}:status`)) || 'offline';
      const lastSeenStr = await this.redisService.get(
        `user:${userId}:last_seen`
      );
      const device = await this.redisService.get(`user:${userId}:device`);

      return {
        userId,
        status: isOnline ? (status as any) : 'offline',
        lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
        device: device || undefined,
      };
    } catch (error) {
      this.logger.error(`获取用户在线状态失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 批量获取用户在线状态
   */
  async getBatchUserOnlineStatus(userIds: string[]): Promise<UserPresence[]> {
    try {
      const promises = userIds.map((userId) =>
        this.getUserOnlineStatus(userId)
      );
      const results = await Promise.all(promises);
      return results.filter((result) => result !== null) as UserPresence[];
    } catch (error) {
      this.logger.error(
        `批量获取用户在线状态失败: ${error.message}`,
        error.stack
      );
      return [];
    }
  }

  /**
   * 获取所有在线用户
   */
  async getAllOnlineUsers(): Promise<OnlineUser[]> {
    try {
      // 从Redis获取所有在线用户的键
      const onlineKeys = await this.redisService.keys('user:*:online');
      const userIds = onlineKeys.map((key) => key.split(':')[1]);

      if (userIds.length === 0) {
        return [];
      }

      // 获取用户详细信息
      const users = await this.userModel
        .find({ _id: { $in: userIds } })
        .select('_id nickname avatar')
        .lean();

      // 获取每个用户的状态信息
      const onlineUsers: OnlineUser[] = [];
      for (const user of users) {
        const userId = user._id.toString();
        const status =
          (await this.redisService.get(`user:${userId}:status`)) || 'online';
        const lastSeenStr = await this.redisService.get(
          `user:${userId}:last_seen`
        );
        const socketId = await this.redisService.get(`user:${userId}:socket`);

        onlineUsers.push({
          id: userId,
          nickname: user.nickname,
          avatar: user.avatar,
          status: status as any,
          lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
          socketId: socketId || undefined,
        });
      }

      return onlineUsers;
    } catch (error) {
      this.logger.error(`获取所有在线用户失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 获取在线用户数量
   */
  async getOnlineUserCount(): Promise<number> {
    try {
      const onlineKeys = await this.redisService.keys('user:*:online');
      return onlineKeys.length;
    } catch (error) {
      this.logger.error(`获取在线用户数量失败: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * 检查用户是否在线
   */
  async isUserOnline(userId: string): Promise<boolean> {
    try {
      const isOnline = await this.redisService.get(`user:${userId}:online`);
      return !!isOnline;
    } catch (error) {
      this.logger.error(`检查用户在线状态失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取用户的Socket ID
   */
  async getUserSocketId(userId: string): Promise<string | null> {
    try {
      return await this.redisService.get(`user:${userId}:socket`);
    } catch (error) {
      this.logger.error(`获取用户Socket ID失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 向特定用户发送消息（通过Redis发布）
   */
  async sendMessageToUser(
    userId: string,
    event: string,
    data: any
  ): Promise<boolean> {
    try {
      const socketId = await this.getUserSocketId(userId);
      if (socketId) {
        // 通过Redis发布消息，WebSocket网关会监听并转发
        await this.redisService.publish(
          'send_to_user',
          JSON.stringify({
            socketId,
            event,
            data,
            timestamp: Date.now(),
          })
        );
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`向用户发送消息失败: ${(error as Error).message}`);
      return false;
    }
  }

  /**
   * 向多个用户发送消息
   */
  async sendMessageToUsers(
    userIds: string[],
    event: string,
    data: any
  ): Promise<number> {
    try {
      let successCount = 0;
      const promises = userIds.map(async (userId) => {
        const success = await this.sendMessageToUser(userId, event, data);
        if (success) successCount++;
      });

      await Promise.all(promises);
      return successCount;
    } catch (error) {
      this.logger.error(
        `向多个用户发送消息失败: ${error.message}`,
        error.stack
      );
      return 0;
    }
  }

  /**
   * 清理过期的在线状态
   */
  async cleanupExpiredOnlineStatus(): Promise<void> {
    try {
      const onlineKeys = await this.redisService.keys('user:*:online');
      const now = new Date();

      for (const key of onlineKeys) {
        const userId = key.split(':')[1];
        const lastSeenStr = await this.redisService.get(
          `user:${userId}:last_seen`
        );

        if (lastSeenStr) {
          const lastSeen = new Date(lastSeenStr);
          const diffMinutes =
            (now.getTime() - lastSeen.getTime()) / (1000 * 60);

          // 如果超过30分钟没有活动，标记为离线
          if (diffMinutes > 30) {
            await this.handleUserOffline(userId);
          }
        }
      }

      this.logger.log('清理过期在线状态完成');
    } catch (error) {
      this.logger.error(`清理过期在线状态失败: ${error.message}`, error.stack);
    }
  }
}
