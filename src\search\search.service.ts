import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { HotKeyWords } from './schemas/hotKeyWords.schema';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';

@Injectable()
export class SearchService {
  constructor(
    @InjectModel(HotKeyWords.name) private hotKeyWordsModel: Model<HotKeyWords>
  ) {}

  /**
   * !: 添加一个热点词
   * @param keyword 热点词
   * @returns 热点词
   */
  async addHotKeyWord(keyword: string) {
    // 先检查是否存在
    const exist = await this.hotKeyWordsModel.findOne({ keyword });
    if (exist) {
      throw new HttpException('热点词已存在', HttpStatusEnum.CONFLICT);
    }
    const hotKeyWord = new this.hotKeyWordsModel({ keyword });
    await hotKeyWord.save();

    return hotKeyWord.toJSON();
  }

  /**
   * !: 获取热点词列表
   * @returns 热点词列表
   */
  async getHotKeyWords() {
    const hotKeyWords = await this.hotKeyWordsModel.find().lean();

    return hotKeyWords.map((item) => item.keyword);
  }

  /**
   * !: 删除热点词
   * @param keyword 热点词
   * @returns 热点词
   */
  async deleteHotKeyWord(keyword: string) {
    const hotKeyWord = await this.hotKeyWordsModel.findOneAndDelete({
      keyword,
    });

    if (!hotKeyWord) {
      throw new HttpException('热点词不存在', HttpStatusEnum.NOT_FOUND);
    }

    return hotKeyWord.toJSON();
  }
}
