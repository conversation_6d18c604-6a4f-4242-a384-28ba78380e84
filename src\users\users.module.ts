import { Module } from '@nestjs/common'; // 导入 NestJS 的 Module 装饰器
import { UsersService } from './service/users.service'; // 导入 UserService 类
import { UsersController } from './users.controller'; // 导入 UserController 类
import { MongooseModule } from '@nestjs/mongoose'; // 导入 MongooseModule 类，用于与 MongoDB 进行交互
import { Users, UsersSchema } from './schemas/users.schema'; // 导入 User 模型及其对应的 Mongoose Schema
import { CounterModule } from 'src/counter/counter.module';
import { CaptchaModule } from 'src/captcha/captcha.module';
import { SocialService } from './service/social.service';
import { UsersFollow, UsersFollowSchema } from './schemas/users-follow.schema';
import { Contents, ContentsSchema } from 'src/contents/schemas/contents.schema';
import {
  ContentsFavorite,
  ContentsFavoriteSchema,
} from 'src/contents/schemas/contents-favorite.schema';
import {
  ContentsLike,
  ContentsLikeSchema,
} from 'src/contents/schemas/contents-like.schema';
import { Photos, PhotosSchema } from 'src/photos/schemas/photos.schema';
import {
  PhotosLike,
  PhotosLikeSchema,
} from 'src/photos/schemas/photos-like.schema';
import {
  PhotosFavorite,
  PhotosFavoriteSchema,
} from 'src/photos/schemas/photos-favorite.schema';
import {
  UsersSetting,
  UsersSettingSchema,
} from './schemas/users-setting.schema';

@Module({
  imports: [
    CounterModule, // 注册 CounterModule 模块
    CaptchaModule, // 注册 CaptchaModule 模块
    MongooseModule.forFeatureAsync([
      {
        name: Users.name,
        useFactory: () => {
          const schema = UsersSchema;
          schema.plugin(require('mongoose-autopopulate'));
          return schema;
        },
      },
    ]),
    MongooseModule.forFeature([
      { name: UsersFollow.name, schema: UsersFollowSchema },
      { name: UsersSetting.name, schema: UsersSettingSchema },
      { name: Contents.name, schema: ContentsSchema },
      { name: ContentsLike.name, schema: ContentsLikeSchema },
      { name: ContentsFavorite.name, schema: ContentsFavoriteSchema },
      { name: Photos.name, schema: PhotosSchema },
      { name: PhotosLike.name, schema: PhotosLikeSchema },
      { name: PhotosFavorite.name, schema: PhotosFavoriteSchema },
    ]),
  ],
  controllers: [UsersController], // 定义该模块的控制器
  providers: [UsersService, SocialService], // 定义该模块的服务提供者
  exports: [UsersService, SocialService], // 导出该模块的服务提供者，以便其他模块可以注入
})
export class UsersModule {} // 导出 UsersModule 类
