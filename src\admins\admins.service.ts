import { HttpException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Users } from 'src/users/schemas/users.schema';
import * as config from 'config';
import { UserRoleEnum } from 'src/shared/enums/User.enum';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { PasswordUtil } from 'src/common/utils';
import { UsersSetting } from 'src/users/schemas/users-setting.schema';
import { RedisService } from 'src/redis/redis.service';
import { Cron } from '@nestjs/schedule';
import { WebsocketGateway } from 'src/websocket/websocket.gateway';

const adminConfig = config.get<DefaultAdminConfig>('defaultAdmin');

@Injectable()
export class AdminsService {
  constructor(
    @InjectModel(Users.name) private readonly userModel: Model<Users>,
    @InjectModel(UsersSetting.name)
    private readonly userSettingModel: Model<UsersSetting>,
    private readonly redisService: RedisService,
    private readonly websocketGateway: WebsocketGateway
  ) {
    this.initAdminAccount();
  }

  /**
   * 初始化管理员账号
   */
  async initAdminAccount() {
    // 检索默认管理员邮箱是否存在
    const admin = await this.userModel.findOne({ email: adminConfig.email });
    if (admin) {
      // 检查管理员身份是否为超级管理员
      if (admin.role === UserRoleEnum.SuperAdmin) {
        Logger.log('默认管理员账号已存在，无需初始化');
        return;
      }
      // 更新管理员角色为超级管理员
      await this.userModel.updateOne(
        { email: adminConfig.email },
        { $set: { role: UserRoleEnum.SuperAdmin } }
      );
      Logger.log('默认管理员账号已存在，已更新角色为超级管理员');
      return;
    }
    // 创建默认管理员账号
    const newAdmin = new this.userModel({
      ...adminConfig,
      password: await PasswordUtil.hash(adminConfig.password),
      role: UserRoleEnum.SuperAdmin,
    });
    await newAdmin.save();
    // 创建默认管理员设置
    const newAdminSetting = new this.userSettingModel({
      user: newAdmin._id.toString(),
    });
    await newAdminSetting.save();
    Logger.log('默认管理员账号创建成功');
    return;
  }

  /**
   * 聚合在线用户
   */
  // 定时任务（每分钟执行）
  @Cron('* * * * *')
  async aggregateOnlineUsers() {
    try {
      // 获取Redis中的活跃用户（来自HTTP请求认证）
      const redisKeys = await this.redisService.keys('online:*');
      const redisUsers = redisKeys.map((k) => k.split(':')[1]);

      // 获取Socket.IO在线用户（来自WebSocket连接）
      const socketUsers: string[] = [];
      if (this.websocketGateway.server) {
        // 从Redis中获取WebSocket在线用户，使用Redis客户端直接操作
        const redisClient = this.redisService.getDataClient();
        const onlineUsersHash = await redisClient.hGetAll('online_users');
        socketUsers.push(...Object.keys(onlineUsersHash));
      }

      // 合并去重
      const onlineUsers = [...new Set([...redisUsers, ...socketUsers])];

      // 存储当前在线人数和用户列表
      await this.redisService.set(
        'online:count',
        onlineUsers.length.toString()
      );
      await this.redisService.set('online:users', JSON.stringify(onlineUsers));

      Logger.log(`聚合在线用户完成，当前在线人数: ${onlineUsers.length}`);
    } catch (error) {
      Logger.error('聚合在线用户失败:', error);
    }
  }

  /**
   * 获取在线用户列表
   * @returns
   */
  async getOnlineUsers() {
    try {
      const usersJson = await this.redisService.get('online:users');
      const userIds: string[] = usersJson
        ? (JSON.parse(usersJson) as string[])
        : [];

      // 获取用户详细信息
      const users = await this.userModel
        .find({ _id: { $in: userIds } })
        .select('_id nickname avatar')
        .lean();

      return {
        count: userIds.length,
        users: users.map((user) => ({
          id: user._id.toString(),
          nickname: user.nickname,
          avatar: user.avatar,
        })),
      };
    } catch (error) {
      Logger.error('获取在线用户列表失败:', error);
      return {
        count: 0,
        users: [],
      };
    }
  }
}
