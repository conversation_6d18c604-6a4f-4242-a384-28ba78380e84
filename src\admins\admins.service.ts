import { HttpException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Users } from 'src/users/schemas/users.schema';
import * as config from 'config';
import { UserRoleEnum } from 'src/shared/enums/User.enum';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { PasswordUtil } from 'src/common/utils';
import { UsersSetting } from 'src/users/schemas/users-setting.schema';
import { RedisService } from 'src/redis/redis.service';
import { Cron } from '@nestjs/schedule';

const adminConfig = config.get<DefaultAdminConfig>('defaultAdmin');

@Injectable()
export class AdminsService {
  constructor(
    @InjectModel(Users.name) private readonly userModel: Model<Users>,
    @InjectModel(UsersSetting.name)
    private readonly userSettingModel: Model<UsersSetting>,
    private readonly redisService: RedisService
  ) {
    this.initAdminAccount();
  }

  /**
   * 初始化管理员账号
   */
  async initAdminAccount() {
    // 检索默认管理员邮箱是否存在
    const admin = await this.userModel.findOne({ email: adminConfig.email });
    if (admin) {
      // 检查管理员身份是否为超级管理员
      if (admin.role === UserRoleEnum.SuperAdmin) {
        Logger.log('默认管理员账号已存在，无需初始化');
        return;
      }
      // 更新管理员角色为超级管理员
      await this.userModel.updateOne(
        { email: adminConfig.email },
        { $set: { role: UserRoleEnum.SuperAdmin } }
      );
      Logger.log('默认管理员账号已存在，已更新角色为超级管理员');
      return;
    }
    // 创建默认管理员账号
    const newAdmin = new this.userModel({
      ...adminConfig,
      password: await PasswordUtil.hash(adminConfig.password),
      role: UserRoleEnum.SuperAdmin,
    });
    await newAdmin.save();
    // 创建默认管理员设置
    const newAdminSetting = new this.userSettingModel({
      user: newAdmin._id.toString(),
    });
    await newAdminSetting.save();
    Logger.log('默认管理员账号创建成功');
    return;
  }

  /**
   * 聚合在线用户
   */
  // 定时任务（每分钟执行）
  @Cron('* * * * *')
  async aggregateOnlineUsers() {
    // 获取Redis中的活跃用户
    const redisKeys = await this.redisService.keys('online:*');
    const redisUsers = redisKeys.map((k) => k.split(':')[1]);

    // 获取Socket.IO在线用户
    const socketRooms = io.sockets.adapter.rooms;
    const socketUsers = [...socketRooms.keys()]
      .filter((k) => k.startsWith('user_'))
      .map((k) => k.split('_')[1]);

    // 合并去重
    const onlineUsers = [...new Set([...redisUsers, ...socketUsers])];

    // 存储当前在线人数
    await this.redisService.set('online:count', onlineUsers.length);
  }

  /**
   * 获取在线人数
   * @returns
   */
  async getOnlineCount() {
    return {
      count: (await this.redisService.get('online:count')) || 0,
    };
  }

  /**
   * 获取在线列表
   * @returns
   */
  async getOnlineUsers() {}
}
