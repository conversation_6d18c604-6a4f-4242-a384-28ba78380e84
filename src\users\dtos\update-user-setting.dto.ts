import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

export class UpdateUserSettingDto {
  /** 是否显示关注列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我关注列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showFollowList: boolean;

  /** 是否显示粉丝列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我粉丝列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showFansList: boolean;

  /** 是否显示收藏的图片列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我收藏的图片列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showFavoritePhotoList: boolean;

  /** 是否显示点赞的图片列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我点赞的图片列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showLikePhotoList: boolean;

  /** 是否显示收藏的帖子列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我收藏的帖子列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showFavoriteContentList: boolean;

  /** 是否显示点赞的帖子列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我点赞的帖子列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showLikeContentList: boolean;

  /** 是否显示我上传的图片列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我上传的图片列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showMyPhotoList: boolean;

  /** 是否显示我发布的帖子列表 */
  @ApiPropertyOptional({
    description: '是否允许别人查看我发布的帖子列表',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  showMyContentList: boolean;

  /** 是否接收邮件 */
  @ApiPropertyOptional({
    description: '是否接收邮件通知',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  receiveEmail: boolean;

  /** 是否接收短信 */
  @ApiPropertyOptional({
    description: '是否接收短信通知',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  receiveSms: boolean;
}
