# WebSocket 使用指南 - 从零开始

## 🎯 快速理解

### 什么是WebSocket？
WebSocket是一种实时通信技术，允许服务器和客户端之间进行双向通信。想象成一个电话线，双方都可以随时说话和听到对方。

### 我们的系统能做什么？
1. **实时聊天** - 用户之间可以即时发送消息
2. **在线状态** - 看到谁在线，谁离线
3. **系统通知** - 服务器可以推送重要消息
4. **群组聊天** - 多人聊天室功能

## 🚀 前端开发者快速上手

### 第一步：安装依赖
```bash
npm install socket.io-client
```

### 第二步：建立连接
```javascript
import { io } from 'socket.io-client';

// 连接到WebSocket服务器
const socket = io('ws://localhost:3000/ws', {
  auth: {
    token: 'Bearer ' + localStorage.getItem('jwt_token') // 你的JWT令牌
  }
});

// 监听连接状态
socket.on('connect', () => {
  console.log('✅ 连接成功！');
});

socket.on('disconnect', () => {
  console.log('❌ 连接断开');
});
```

### 第三步：监听事件
```javascript
// 监听其他用户上线
socket.on('user_online', (data) => {
  console.log('👋 用户上线:', data.user.nickname);
  // 在UI中显示用户上线
});

// 监听其他用户下线
socket.on('user_offline', (data) => {
  console.log('👋 用户下线:', data.userId);
  // 在UI中移除用户
});

// 监听私聊消息
socket.on('private_message', (data) => {
  console.log('💬 收到消息:', data.content, '来自:', data.from);
  // 在聊天界面显示消息
});
```

### 第四步：发送消息
```javascript
// 发送私聊消息
function sendPrivateMessage(receiverId, message) {
  socket.emit('private_message', {
    receiverId: receiverId,
    content: message
  });
}

// 更新自己的状态
function updateMyStatus(status) {
  socket.emit('update_user_status', { status }); // 'online', 'away', 'busy'
}

// 获取在线用户列表
function getOnlineUsers() {
  socket.emit('get_all_online_users');
}

// 监听在线用户列表响应
socket.on('all_online_users_response', (response) => {
  if (response.success) {
    console.log('在线用户:', response.data.users);
    // 更新UI中的在线用户列表
  }
});
```

## 📱 完整的聊天应用示例

### HTML结构
```html
<!DOCTYPE html>
<html>
<head>
    <title>实时聊天</title>
</head>
<body>
    <div id="app">
        <!-- 在线状态 -->
        <div id="status">
            <span>我的状态:</span>
            <select id="statusSelect">
                <option value="online">在线</option>
                <option value="away">离开</option>
                <option value="busy">忙碌</option>
            </select>
        </div>

        <!-- 在线用户列表 -->
        <div id="onlineUsers">
            <h3>在线用户 (<span id="userCount">0</span>)</h3>
            <ul id="userList"></ul>
        </div>

        <!-- 聊天区域 -->
        <div id="chatArea">
            <div id="messages"></div>
            <div id="inputArea">
                <input type="text" id="messageInput" placeholder="输入消息...">
                <button onclick="sendMessage()">发送</button>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="chat.js"></script>
</body>
</html>
```

### JavaScript实现 (chat.js)
```javascript
class ChatApp {
    constructor() {
        this.socket = null;
        this.currentUser = null;
        this.onlineUsers = [];
        this.init();
    }

    // 初始化应用
    init() {
        this.connectSocket();
        this.bindEvents();
    }

    // 连接WebSocket
    connectSocket() {
        const token = localStorage.getItem('jwt_token');
        if (!token) {
            alert('请先登录！');
            return;
        }

        this.socket = io('ws://localhost:3000/ws', {
            auth: { token: `Bearer ${token}` }
        });

        // 连接成功
        this.socket.on('connect', () => {
            this.showMessage('系统', '连接成功！', 'system');
            this.getOnlineUsers(); // 获取在线用户列表
        });

        // 连接失败
        this.socket.on('connect_error', (error) => {
            this.showMessage('系统', '连接失败: ' + error.message, 'error');
        });

        // 用户上线
        this.socket.on('user_online', (data) => {
            this.addOnlineUser(data.user);
            this.showMessage('系统', `${data.user.nickname} 上线了`, 'system');
        });

        // 用户下线
        this.socket.on('user_offline', (data) => {
            this.removeOnlineUser(data.userId);
            this.showMessage('系统', `用户 ${data.userId} 下线了`, 'system');
        });

        // 收到私聊消息
        this.socket.on('private_message', (data) => {
            this.showMessage(data.from, data.content, 'received');
        });

        // 在线用户列表响应
        this.socket.on('all_online_users_response', (response) => {
            if (response.success) {
                this.updateOnlineUsersList(response.data.users);
                this.updateUserCount(response.data.count);
            }
        });

        // 状态更新响应
        this.socket.on('status_update_response', (response) => {
            if (response.success) {
                this.showMessage('系统', `状态已更新为: ${response.status}`, 'system');
            }
        });
    }

    // 绑定UI事件
    bindEvents() {
        // 状态选择
        document.getElementById('statusSelect').addEventListener('change', (e) => {
            this.updateStatus(e.target.value);
        });

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
    }

    // 发送消息
    sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();
        
        if (!message) return;

        // 这里简化处理，实际应用中需要选择接收者
        const receiverId = 'target_user_id'; // 需要从UI中获取
        
        this.socket.emit('private_message', {
            receiverId: receiverId,
            content: message
        });

        // 显示自己发送的消息
        this.showMessage('我', message, 'sent');
        input.value = '';
    }

    // 更新状态
    updateStatus(status) {
        this.socket.emit('update_user_status', { status });
    }

    // 获取在线用户
    getOnlineUsers() {
        this.socket.emit('get_all_online_users');
    }

    // 显示消息
    showMessage(sender, content, type) {
        const messagesDiv = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const time = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `
            <span class="time">[${time}]</span>
            <span class="sender">${sender}:</span>
            <span class="content">${content}</span>
        `;
        
        messagesDiv.appendChild(messageDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    // 添加在线用户
    addOnlineUser(user) {
        if (!this.onlineUsers.find(u => u.id === user.id)) {
            this.onlineUsers.push(user);
            this.renderOnlineUsers();
        }
    }

    // 移除在线用户
    removeOnlineUser(userId) {
        this.onlineUsers = this.onlineUsers.filter(u => u.id !== userId);
        this.renderOnlineUsers();
    }

    // 更新在线用户列表
    updateOnlineUsersList(users) {
        this.onlineUsers = users;
        this.renderOnlineUsers();
    }

    // 渲染在线用户列表
    renderOnlineUsers() {
        const userList = document.getElementById('userList');
        userList.innerHTML = '';
        
        this.onlineUsers.forEach(user => {
            const li = document.createElement('li');
            li.innerHTML = `
                <img src="${user.avatar}" alt="${user.nickname}" width="20" height="20">
                <span>${user.nickname}</span>
                <span class="status ${user.status}">${user.status}</span>
            `;
            userList.appendChild(li);
        });
    }

    // 更新用户数量
    updateUserCount(count) {
        document.getElementById('userCount').textContent = count;
    }
}

// 启动应用
window.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});

// 全局函数供HTML调用
function sendMessage() {
    window.chatApp.sendMessage();
}
```

### CSS样式 (可选)
```css
#app {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

#onlineUsers {
    border: 1px solid #ccc;
    padding: 10px;
    margin-bottom: 20px;
}

#userList {
    list-style: none;
    padding: 0;
}

#userList li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 5px;
    border-bottom: 1px solid #eee;
}

#chatArea {
    border: 1px solid #ccc;
    height: 400px;
    display: flex;
    flex-direction: column;
}

#messages {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    border-bottom: 1px solid #eee;
}

.message {
    margin-bottom: 10px;
}

.message.sent {
    text-align: right;
    color: blue;
}

.message.received {
    text-align: left;
    color: green;
}

.message.system {
    text-align: center;
    color: gray;
    font-style: italic;
}

#inputArea {
    padding: 10px;
    display: flex;
    gap: 10px;
}

#messageInput {
    flex: 1;
    padding: 5px;
}

.status.online { color: green; }
.status.away { color: orange; }
.status.busy { color: red; }
.status.offline { color: gray; }
```

## 🔧 常见问题解决

### 1. 连接失败
```javascript
socket.on('connect_error', (error) => {
    console.error('连接失败:', error.message);
    
    if (error.message === '用户未登录') {
        // 跳转到登录页面
        window.location.href = '/login';
    }
});
```

### 2. 重连机制
```javascript
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;

socket.on('disconnect', (reason) => {
    console.log('连接断开:', reason);
    
    if (reason === 'io server disconnect') {
        // 服务器主动断开，尝试重连
        if (reconnectAttempts < maxReconnectAttempts) {
            setTimeout(() => {
                reconnectAttempts++;
                socket.connect();
            }, 1000 * reconnectAttempts);
        }
    }
});

socket.on('connect', () => {
    reconnectAttempts = 0; // 重置重连次数
});
```

### 3. 调试技巧
```javascript
// 开启调试模式
localStorage.debug = 'socket.io-client:socket';

// 监听所有事件
socket.onAny((event, ...args) => {
    console.log(`收到事件: ${event}`, args);
});

// 监听发送的事件
socket.onAnyOutgoing((event, ...args) => {
    console.log(`发送事件: ${event}`, args);
});
```

## 📚 下一步学习

1. **深入了解**: 阅读 [系统架构文档](./websocket-system-architecture.md)
2. **高级功能**: 查看 [用户状态管理文档](./websocket-user-status.md)
3. **API参考**: 参考 [完整API文档](./websocket-usage.md)

这个指南应该能帮助你快速上手我们的WebSocket系统！如果有任何问题，请查看相关文档或联系开发团队。
