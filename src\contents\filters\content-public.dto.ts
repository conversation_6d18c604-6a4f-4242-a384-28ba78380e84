import { Expose, Type } from 'class-transformer';

class UserInfoDto {
  @Expose()
  uid: string; // 用户UID
  @Expose()
  nickname: string; // 昵称
  @Expose()
  avatar: string; // 头像
}

class PhotoAttributesDto {
  @Expose()
  size: number; // 图片大小
  @Expose()
  width: number; // 图片宽度
  @Expose()
  height: number; // 图片高度
  @Expose()
  format: string; // 图片格式
}

class photoInfoDto {
  @Expose()
  id: string; // 图片ID
  @Expose()
  filename: string; // 图片名称
  @Expose()
  url: string; // 封面地址
  @Expose()
  @Type(() => PhotoAttributesDto)
  attributes: PhotoAttributesDto; // 图片属性
}

export class ContentPublicInfoDto {
  @Expose()
  id: string; // 帖子ID
  @Expose()
  @Type(() => UserInfoDto)
  user: UserInfoDto; // 所属用户信息
  @Expose()
  title: string; // 帖子标题
  @Expose()
  content: string; // 帖子内容
  @Expose()
  category: string; // 帖子分类
  @Expose()
  tags: string[]; // 帖子标签
  @Expose()
  @Type(() => photoInfoDto)
  photos: photoInfoDto[]; // 帖子图片
  @Expose()
  favoriteCount: number; // 帖子收藏数
  @Expose()
  likeCount: number; // 点赞量
  @Expose()
  isLiked: boolean; // 是否点赞
  @Expose()
  isFavorited: boolean; // 是否收藏
}
