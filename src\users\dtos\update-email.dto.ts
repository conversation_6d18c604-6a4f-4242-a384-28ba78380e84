import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Length } from 'class-validator';

export class UpdateEmailDto {
  /** 旧邮箱 */
  @ApiProperty({ description: '旧邮箱', example: '<EMAIL>' })
  @IsNotEmpty({ message: '旧邮箱不能为空' })
  @IsString({ message: '邮箱必须为字符串' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  oldEmail: string;

  /** 新邮箱 */
  @ApiProperty({ description: '新邮箱', example: '<EMAIL>' })
  @IsNotEmpty({ message: '新邮箱不能为空' })
  @IsString({ message: '邮箱必须为字符串' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  email: string;

  /** 验证码 */
  @ApiProperty({ description: '验证码', example: '123456' })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码必须为字符串' })
  @Length(6, 6, { message: '验证码长度必须为6位' })
  code: string;
}
