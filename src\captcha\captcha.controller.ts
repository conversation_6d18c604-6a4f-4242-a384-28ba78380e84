import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Req,
  Res,
} from '@nestjs/common';
import { Public } from '../common/decorator/public.decorator'; // 引入 Public 装饰器，标记路由为公共，无需验证
import { Request, Response } from 'express';
import { formatResponse } from 'src/common/utils';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { sendVerificationPhoneDto } from './dtos/sendVerificationPhone.dto';
import { sendVerificationEmailDto } from './dtos/sendVerificationEmail.dto';
import { CaptchaService } from './captcha.service';
import { join } from 'path';

@Controller('captcha') // 定义控制器路由为 'captcha'
export class CaptchaController {
  constructor(
    private readonly captchaService: CaptchaService // 注入 CaptchaService
  ) {}

  @ApiOperation({ summary: '获取svg图像验证码-公开路由' })
  @ApiOkResponse({ description: '返回图像验证码' })
  // !: 获取图像验证码
  @Get('image')
  @Public() // 使用 Public 装饰器标记该路由为公共路由
  getCaptcha(@Res() res: Response) {
    const { svg_code, svg_token } = this.captchaService.getCaptcha();
    res.cookie('svg_token', svg_token, {
      httpOnly: true, // 禁止 JavaScript 读取
      secure: true, // 仅通过 HTTPS 传输
      sameSite: 'none', // 跨域时允许 Cookie 被发送
      maxAge: 7 * 24 * 3600 * 1000, // 过期时间（毫秒）
    });
    res.status(200).type('image/svg+xml').send(svg_code);
  }

  @ApiOperation({ summary: '发送邮件验证码-公开路由' })
  @ApiOkResponse({ description: '返回邮件发送状态' })
  // !: 用户邮件验证码发送接口
  @Post('send-email-captcha')
  @Public() // 使用 Public 装饰器标记该路由为公共路由
  async sendVerificationEmail(
    @Body()
    sendVerificationEmailDto: sendVerificationEmailDto,
    @Req() req: Request,
    @Res() res: Response
  ) {
    // 获取验证token
    const { svg_token } = req.cookies;
    if (!svg_token) {
      throw new HttpException('验证码无效', HttpStatusEnum.BAD_REQUEST);
    }
    const { email, code } = sendVerificationEmailDto;
    await this.captchaService.verifyCaptcha(svg_token as string, code);
    await this.captchaService.sendEmailCaptcha(email);
    res.status(200).json(formatResponse(200, '验证码已发送至邮箱'));
  }

  @ApiOperation({ summary: '发送手机验证码-公开路由' })
  @ApiOkResponse({ description: '返回手机验证码发送状态' })
  // !: 用户注册手机验证码发送接口
  @Post('send-sms-captcha')
  @Public() // 使用 Public 装饰器标记该路由为公共路由
  async sendVerificationSMS(
    @Body()
    sendVerificationPhoneDto: sendVerificationPhoneDto,
    @Req() req: Request,
    @Res() res: Response
  ) {
    // 获取验证token
    const { svg_token } = req.cookies;
    if (!svg_token) {
      throw new HttpException('验证码无效', HttpStatusEnum.BAD_REQUEST);
    }
    const { phone, code } = sendVerificationPhoneDto;
    await this.captchaService.verifyCaptcha(svg_token as string, code);
    await this.captchaService.sendSMS(phone);
    res.status(200).json(formatResponse(200, '验证码已发送至手机'));
  }

  @ApiOperation({ summary: '邮箱退订-公开路由' })
  @ApiOkResponse({ description: '返回退订结果' })
  // !: 定义一个GET请求，用于邮箱退订
  @Get('unsubscribe/:token')
  @Public() // 使用 Public 装饰器标记该路由为公共路由
  async unsubscribe(@Req() req: Request, @Res() res: Response) {
    const { token } = req.params;
    await this.captchaService.unsubscribeEmail(token);
    //  返回退订成功页面
    res
      .status(200)
      .type('text/html')
      .sendFile(join(__dirname, '..', 'public', 'unsubscribe.html'));
  }

  @ApiOperation({ summary: '邮箱订阅-公开路由' })
  @ApiOkResponse({ description: '返回订阅结果' })
  // !: 定义一个GET请求，用于邮箱订阅
  @Get('subscribe/:email')
  @Public() // 使用 Public 装饰器标记该路由为公共路由
  async subscribe(@Req() req: Request, @Res() res: Response) {
    const { email } = req.params;
    await this.captchaService.subscribeEmail(email);
    //  返回订阅成功页面
    res
      .status(200)
      .type('text/html')
      .sendFile(join(__dirname, '..', 'public', 'subscribe.html'));
  }
}
