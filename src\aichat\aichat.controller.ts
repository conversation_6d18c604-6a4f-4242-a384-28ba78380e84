import { Body, Controller, Post, Res } from '@nestjs/common';
import { Response } from 'express';
import { AichatService } from './aichat.service';
import { CreateChatDto } from './dto/create-chat.dto';
import { ValidationPipe } from '@nestjs/common';
import { ApiBody, ApiOperation } from '@nestjs/swagger';
import { Public } from 'src/common/decorator/public.decorator';

@Controller('chat')
export class AichatController {
  constructor(private readonly aichatService: AichatService) {}

  @ApiOperation({ summary: '创建ai聊天-公共路由' }) // 描述API的功能
  @ApiBody({ type: CreateChatDto }) // 描述请求体的结构
  @Post('/stream')
  @Public() // 标记为公共API
  async chatStream(
    @Body(new ValidationPipe()) { message, identity }: CreateChatDto,
    @Res() res: Response
  ): Promise<void> {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no');

    await this.aichatService.handleChatStream(message, identity, res);
  }
}
