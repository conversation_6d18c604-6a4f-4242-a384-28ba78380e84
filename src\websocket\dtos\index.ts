/**
 * WebSocket 数据传输对象 (DTO) 定义
 *
 * 这些类定义了客户端和服务端之间传递的消息格式
 * 确保数据的类型安全和结构一致性
 */

/**
 * 系统通知数据传输对象
 *
 * 用于管理员向所有用户发送系统级通知
 * 只有具有管理员权限的用户才能发送此类消息
 *
 * 使用示例:
 * ```javascript
 * socket.emit('system_notice', {
 *   type: 'announcement',
 *   content: '系统将于今晚22:00进行维护，预计持续2小时'
 * });
 * ```
 */
export class SystemNoticeDto {
  /**
   * 通知类型
   * - 'alert': 警告类型，通常用于紧急通知
   * - 'announcement': 公告类型，用于一般性通知
   */
  type: 'alert' | 'announcement';

  /**
   * 通知内容
   * 支持纯文本，建议长度不超过500字符
   */
  content: string;
}

/**
 * 私聊消息数据传输对象
 *
 * 用于用户之间的一对一私聊通信
 * 消息会被发送给指定的接收者
 *
 * 使用示例:
 * ```javascript
 * socket.emit('private_message', {
 *   receiverId: 'user123',
 *   content: '你好，最近怎么样？'
 * });
 * ```
 */
export class PrivateMessageDto {
  /**
   * 接收者用户ID
   * 必须是有效的用户ID，系统会验证用户是否存在
   */
  receiverId: string;

  /**
   * 消息内容
   * 支持纯文本，建议长度不超过1000字符
   * 系统会自动过滤恶意内容和敏感词汇
   */
  content: string;
}

/**
 * 群聊消息数据传输对象
 *
 * 用于群组内的多人聊天通信
 * 消息会被发送给群组内的所有成员
 * 用户必须先加入群组才能发送群聊消息
 *
 * 使用示例:
 * ```javascript
 * // 先加入群组
 * socket.emit('join_group', 'group456');
 *
 * // 然后发送群聊消息
 * socket.emit('group_message', {
 *   groupId: 'group456',
 *   content: '大家好！'
 * });
 * ```
 */
export class GroupMessageDto {
  /**
   * 群组ID
   * 必须是有效的群组ID，用户必须已加入该群组
   */
  groupId: string;

  /**
   * 消息内容
   * 支持纯文本，建议长度不超过1000字符
   * 系统会自动过滤恶意内容和敏感词汇
   */
  content: string;
}

/**
 * 用户状态更新数据传输对象
 *
 * 用于用户更新自己的在线状态
 * 状态变化会广播给所有连接的用户
 *
 * 使用示例:
 * ```javascript
 * socket.emit('update_user_status', {
 *   status: 'away'
 * });
 * ```
 */
export class UpdateUserStatusDto {
  /**
   * 用户状态
   * - 'online': 在线 (默认状态)
   * - 'away': 离开 (暂时离开但保持连接)
   * - 'busy': 忙碌 (在线但不希望被打扰)
   * - 'offline': 离线 (仅在断开连接时自动设置)
   */
  status: 'online' | 'away' | 'busy' | 'offline';
}

/**
 * 获取用户状态数据传输对象
 *
 * 用于查询指定用户的在线状态信息
 *
 * 使用示例:
 * ```javascript
 * socket.emit('get_user_status', {
 *   userId: 'user123'
 * });
 * ```
 */
export class GetUserStatusDto {
  /**
   * 要查询的用户ID
   */
  userId: string;
}

/**
 * 批量获取用户状态数据传输对象
 *
 * 用于一次性查询多个用户的在线状态
 * 建议单次查询的用户数量不超过50个
 *
 * 使用示例:
 * ```javascript
 * socket.emit('get_batch_user_status', {
 *   userIds: ['user1', 'user2', 'user3']
 * });
 * ```
 */
export class GetBatchUserStatusDto {
  /**
   * 要查询的用户ID数组
   * 建议数组长度不超过50个元素
   */
  userIds: string[];
}
