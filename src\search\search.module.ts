import { Module } from '@nestjs/common';
import { SearchService } from './search.service';
import { SearchController } from './search.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { HotKeyWords, HotKeyWordsSchema } from './schemas/hotKeyWords.schema';

@Module({
  imports: [
    MongooseModule.forFeatureAsync([
      {
        name: HotKeyWords.name,
        useFactory: () => {
          const schema = HotKeyWordsSchema;
          schema.plugin(require('mongoose-autopopulate'));
          return schema;
        },
      },
    ]),
  ],
  controllers: [SearchController],
  providers: [SearchService],
  exports: [SearchService],
})
export class SearchModule {}
