import { Controller, Body, UseGuards, Get } from '@nestjs/common';
import { AdminsService } from './admins.service';
import { RolesGuard } from 'src/common/guard/roles.guard';
import { Roles } from 'src/common/decorator/roles.decorator';

@Controller('admins')
@UseGuards(RolesGuard)
export class AdminsController {
  constructor(private readonly adminsService: AdminsService) {}

  @Get('online')
  @Roles('admin', 'super_admin')
  async getOnlineUsers() {
    return this.adminsService.getOnlineUsers();
  }
}
