import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({
  // 禁用 __v 版本字段
  versionKey: false,
})
export class UsersFollow {
  /** 关注者 */
  @Prop({
    type: String,
    ref: 'Users',
    required: true,
    index: true,
  })
  follower: string;

  /** 被关注者 */
  @Prop({
    type: String,
    ref: 'Users',
    required: true,
    index: true,
  })
  following: string;

  /** 关注时间 */
  @Prop({ default: Date.now })
  createTime: Date;
}

// 使用SchemaFactory创建Schema实例
export const UsersFollowSchema = SchemaFactory.createForClass(UsersFollow);
