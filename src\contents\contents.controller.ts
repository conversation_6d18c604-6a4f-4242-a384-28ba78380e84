import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Req,
  Put,
  Query,
} from '@nestjs/common';
import { ContentsService } from './contents.service';
import { CreateContentDto } from './dtos/create-content.dto';
import { UpdateContentDto } from './dtos/update-content.dto';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { Request } from 'express';
import { formatResponse } from 'src/common/utils';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { plainToClass } from 'class-transformer';
import { ContentProfileInfoDto } from './filters/content-profile.dto';
import { Public } from 'src/common/decorator/public.decorator';
import { SearchAllContentDto } from './dtos/search-content.dto';
import { ContentPublicInfoDto } from './filters/content-public.dto';
import { ContentListInfoDto } from './filters/content-list.dto';

@Controller('contents')
export class ContentsController {
  constructor(private readonly contentsService: ContentsService) {}

  @ApiOperation({ summary: '创建帖子-用户路由' })
  @ApiOkResponse({ description: '帖子创建成功' })
  // !: 定义POST请求处理方法，用于创建帖子
  @Post()
  async createContent(
    @Body() createContentDto: CreateContentDto,
    @Req() req: Request
  ) {
    const content = await this.contentsService.create(
      req.user.id,
      createContentDto
    );
    return formatResponse(
      HttpStatusEnum.OK,
      '创建成功',
      plainToClass(ContentProfileInfoDto, content, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '获取帖子详情-用户路由' })
  @ApiOkResponse({ description: '帖子详情获取成功' })
  // !: 定义Get请求处理方法，用于获取用户自己上传的id帖子详情
  @Get(':id')
  async findContent(@Param('id') id: string, @Req() req: Request) {
    const content = await this.contentsService.findOneById(id, req.user.id);

    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      plainToClass(ContentProfileInfoDto, content, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '修改帖子-用户路由' })
  @ApiOkResponse({ description: '帖子修改成功' })
  // !: 定义Put请求处理方法，用于更新帖子
  @Put(':id')
  async updateContent(
    @Param('id') id: string,
    @Body() updateContentDto: UpdateContentDto,
    @Req() req: Request
  ) {
    const content = await this.contentsService.update(
      req.user.id,
      id,
      updateContentDto
    );
    return formatResponse(
      HttpStatusEnum.OK,
      '更新成功',
      plainToClass(ContentProfileInfoDto, content, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '删除帖子-用户路由' })
  @ApiOkResponse({ description: '帖子删除成功' })
  // !: 定义Delete请求处理方法，用于删除帖子
  @Delete(':id')
  async deleteContent(@Param('id') id: string, @Req() req: Request) {
    await this.contentsService.delete(req.user.id, id);
    return formatResponse(HttpStatusEnum.OK, '删除成功');
  }

  @ApiOperation({ summary: '获取全部帖子列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回帖子列表' })
  // !: 定义Get请求处理方法，用于获取全部帖子列表
  @Get('public/all')
  @Public() // 定义路由为公开路由，不用登录即可访问
  async findAllContent(
    @Query() searchAllContentDto: SearchAllContentDto,
    @Req() req: Request
  ) {
    const data = await this.contentsService.findList(
      searchAllContentDto,
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((content) => {
        return plainToClass(ContentListInfoDto, content, {
          excludeExtraneousValues: true,
        });
      }),
    });
  }

  @ApiOperation({ summary: '获取指定帖子信息-公开路由' })
  @ApiOkResponse({ description: '获取成功返回帖子信息' })
  // !: 定义Get请求处理方法，用于获取指定id帖子详情
  @Get('public/:id')
  @Public() // 定义路由为公开路由，不用登录即可访问
  async findContentPublicInfo(@Param('id') id: string, @Req() req: Request) {
    const content = await this.contentsService.findOneById(
      id,
      req.user ? req.user.id : undefined
    );
    this.contentsService.increaseViewCount(id);
    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      plainToClass(ContentPublicInfoDto, content, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '获取帖子列表-公开路由' })
  @ApiOkResponse({ description: '帖子列表获取成功' })
  // !: 定义Get请求处理方法，用于获取指定用户帖子列表
  @Get('public/all/:uid')
  @Public() // 定义路由为公开路由，不用登录即可访问
  async findContentListByUser(
    @Query() searchAllContentDto: SearchAllContentDto,
    @Param('uid') uid: string,
    @Req() req: Request
  ) {
    const data = await this.contentsService.findList(
      {
        uid,
        ...searchAllContentDto,
      },
      req.user ? req.user.id : undefined
    );

    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((content) => {
        return plainToClass(ContentPublicInfoDto, content, {
          excludeExtraneousValues: true,
        });
      }),
    });
  }

  @ApiOperation({ summary: '点赞或取消点赞帖子-用户路由' })
  @ApiOkResponse({ description: '点赞或取消点赞成功' })
  // !: 定义POST请求处理方法，用于点赞或取消点赞帖子
  @Post('like/:id')
  async likeContent(@Param('id') id: string, @Req() req: Request) {
    const content = await this.contentsService.toggleLikeContent(
      req.user.id,
      id
    );
    return formatResponse(
      HttpStatusEnum.OK,
      content ? '点赞成功' : '取消点赞成功'
    );
  }

  @ApiOperation({ summary: '收藏或取消收藏帖子-用户路由' })
  @ApiOkResponse({ description: '收藏或取消收藏成功' })
  // !: 定义POST请求处理方法，用于收藏或取消收藏帖子
  @Post('favorite/:id')
  async favoriteContent(@Param('id') id: string, @Req() req: Request) {
    const content = await this.contentsService.toggleFavoriteContent(
      req.user.id,
      id
    );
    return formatResponse(
      HttpStatusEnum.OK,
      content ? '收藏成功' : '取消收藏成功'
    );
  }
}
