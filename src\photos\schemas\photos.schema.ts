import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { PhotoStatusEnum } from 'src/shared/enums/Photo.enum';

export type PhotosDocument = HydratedDocument<Photos>;

@Schema({
  timestamps: { createdAt: 'createTime', updatedAt: 'updateTime' },
  // 禁用 __v 版本字段
  versionKey: false,
  toJSON: {
    virtuals: true,
    transform: (_, ret: PhotosDocument) => {
      if (ret.isDeleted) {
        return null;
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-unsafe-assignment
      const { _id, id, ...rest } = ret;
      return {
        id: _id.toString(),
        ...rest,
      };
    },
  },
})
export class Photos {
  /** 图片唯一标识符 */
  @Prop({})
  id: string;

  /** 图片上传者ID */
  @Prop({
    type: String,
    ref: 'Users',
    required: true,
  })
  user: string;

  /** 图片名称 */
  @Prop({
    type: String,
    required: true,
  })
  filename: string;

  /** 下载图片名称 */
  @Prop({
    type: String,
    required: true,
  })
  downloadFilename: string;

  /** 图片地址 */
  @Prop({
    type: String,
    required: true,
  })
  url: string;

  /** 分类 */
  @Prop({
    type: String,
    required: true,
  })
  category: string;

  /** 标签 */
  @Prop({
    type: [String],
    required: true,
  })
  tags: string[];

  /** 收藏量 */
  @Prop({
    type: Number,
    default: 0,
  })
  favoriteCount: number;

  /** 点赞量 */
  @Prop({
    type: Number,
    default: 0,
  })
  likeCount: number;

  /** 浏览量 */
  @Prop({
    type: Number,
    default: 0,
  })
  viewCount: number;

  /** 下载量 */
  @Prop({
    type: Number,
    default: 0,
  })
  downloadCount: number;

  /** 图片属性 */
  @Prop({
    type: {
      size: Number,
      width: Number,
      height: Number,
      format: String,
    },
    required: true,
    _id: false,
  })
  attributes: PhotoAttributes;

  /** 发布状态 */
  @Prop({
    enum: PhotoStatusEnum,
    default: PhotoStatusEnum.Draft,
  })
  status: PhotoStatusEnum;

  /** 是否允许他人查看 */
  @Prop({
    type: Boolean,
    default: true,
  })
  isPublic: boolean;

  /** 删除状态 */
  @Prop({
    type: Boolean,
    default: false,
  })
  isDeleted: boolean;

  /** 上传时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  createTime: Date;

  /** 更新时间 */
  @Prop({
    type: Date,
    default: Date.now,
  })
  updateTime: Date;
}

export const PhotosSchema = SchemaFactory.createForClass(Photos);
