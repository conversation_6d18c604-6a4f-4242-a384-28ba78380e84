import { HttpException, Injectable } from '@nestjs/common';
import { CreateContentDto } from './dtos/create-content.dto';
import { UpdateContentDto } from './dtos/update-content.dto';
import { Model, PipelineStage, Types } from 'mongoose';
import { Contents, ContentsDocument } from './schemas/contents.schema';
import { InjectModel } from '@nestjs/mongoose';
import { UsersService } from 'src/users/service/users.service';
import { Users } from 'src/users/schemas/users.schema';
import { ContentsLike } from './schemas/contents-like.schema';
import { ContentsFavorite } from './schemas/contents-favorite.schema';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { isFavoritedContent, isLikedContent } from 'src/common/utils';
import { Photos } from 'src/photos/schemas/photos.schema';

/** 帖子服务 */
@Injectable()
export class ContentsService {
  constructor(
    @InjectModel(Contents.name) private readonly contentModel: Model<Contents>,
    @InjectModel(ContentsLike.name)
    private contentsLikeModel: Model<ContentsLike>,
    @InjectModel(ContentsFavorite.name)
    private contentsFavoriteModel: Model<ContentsFavorite>,
    @InjectModel(Users.name) private readonly usersModel: Model<Users>,
    @InjectModel(Photos.name) private readonly photosModel: Model<Photos>,
    private readonly usersService: UsersService
  ) {}

  /**
   * !: 创建帖子的异步方法
   * @param userId 用户 ID
   * @param createContentDto 帖子创建 DTO
   * @returns 创建后的帖子
   * @throws HttpException 用户不存在
   */
  async create(userId: string, createContentDto: CreateContentDto) {
    // 查看用户是否有效
    const user = await this.usersService.findOneById(userId);
    // 获取用户设置

    const userSettings = await this.usersService.getSetting(user.uid);
    const content = new this.contentModel({
      user: userId,
      ...createContentDto,
      isPublic: userSettings.showMyContentList,
    });

    await content.save();
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { contentsCount: 1 },
    });
    // 根据id查找帖子
    return content.toJSON();
  }

  /**
   * !: 更新帖子的异步方法
   * @param userId 用户 ID
   * @param id 帖子 ID
   * @param updateContentDto 帖子更新 DTO
   * @returns 更新后的帖子
   */
  async update(userId: string, id: string, updateContentDto: UpdateContentDto) {
    const content = await this.contentModel.findById(id);
    if (!content?.toJSON()) {
      throw new HttpException('帖子不存在', 404);
    }
    // 查看用户是否有效
    await this.usersService.findOneById(userId);

    // 查看用户是否是作者
    if (content.toJSON().user !== userId) {
      throw new HttpException('不是作者，无权修改', 403);
    }
    content.set(updateContentDto);
    await content.save();
    return content.toJSON();
  }

  /**
   * !: 删除帖子的异步方法
   * @param userId 用户 ID
   * @param id 帖子 ID
   * @returns 删除成功的布尔值
   * @throws HttpException 帖子不存在
   * @throws HttpException 不是作者，无权删除
   */
  async delete(userId: string, id: string) {
    // 查看帖子是否存在
    const content = await this.contentModel.findById(id);
    if (!content?.toJSON()) {
      throw new HttpException('帖子不存在', 404);
    }
    // 查看用户是否为帖子拥有者
    if (content.user !== userId) {
      throw new HttpException('不是作者，无权删除', 403);
    }
    // 标记帖子为删除状态
    content.isDeleted = true;
    await content.save();
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { contentsCount: -1 },
    });
    return true;
  }

  /**
   * !: 根据 ID 查找帖子的异步方法
   * @param id 帖子 ID
   * @param userId 用户 ID
   * @returns 帖子
   * @throws HttpException 帖子不存在
   */
  async findOneById(id: string, userId?: string) {
    const content = await this.contentModel
      .findById(id)
      .populate('user photos', 'uid nickname avatar url filename attributes');
    if (!content?.toJSON()) {
      throw new HttpException('帖子不存在', 404);
    }

    // 当帖子为非公开状态时要检查用户id
    if (!content.isPublic) {
      // 必须传入用户id验证所属用户是否为当前用户
      // 附加所属用户信息
      const user = await this.usersService.findOneById(content.user);

      if (!userId || user.id !== userId) {
        throw new HttpException('无权查看此帖子', 403);
      }
    }
    const pipeline: PipelineStage[] = [
      { $match: { _id: new Types.ObjectId(id) } },
      ...isLikedContent(userId),
      ...isFavoritedContent(userId),
    ];

    const data = await this.contentModel.aggregate<Contents>(pipeline);

    return { ...data[0], ...content.toJSON() };
  }

  /**
   * !: 获取全部帖子列表的异步方法
   * @param options 选项
   * @param userId 用户 ID
   * @returns 帖子列表
   * @throws HttpException 用户不存在
   */
  async findList(
    options: Partial<Contents> & PaginationQuery<Contents> & { uid?: string },
    userId?: string
  ) {
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'asc',
      ...rest
    } = options;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const query = { isDeleted: false, isPublic: true, status: 'approved' };

    // 如果需要查询固定用户的帖子
    if (rest.uid) {
      // 查看用户是否存在
      const user = await this.usersService.findOneByUid(rest.uid);

      // 检查用户是否允许他人查看列表
      const settings = await this.usersService.getSetting(user.uid);

      if (!settings?.showMyContentList) {
        if (user.id !== userId) {
          throw new HttpException(
            '隐私设置不允许查看',
            HttpStatusEnum.BAD_REQUEST
          );
        }
        query.isPublic = false;
      }
      query['user'] = user.id;
    }
    // 分类筛选
    if (rest.category) query['category'] = rest.category;
    // 标签筛选
    if (rest.tags) query['tags'] = { $in: rest.tags };
    // 如果有搜索字符串,则需要进行模糊查询
    if (rest.keyword) {
      query['$or'] = [
        // 标题模糊查询
        { title: { $regex: rest.keyword, $options: 'i' } },
        // 内容模糊查询
        { content: { $regex: rest.keyword, $options: 'i' } },
        // 分类模糊查询
        { category: { $regex: rest.keyword, $options: 'i' } },
        // 标签模糊查询
        { tags: { $elemMatch: { $regex: rest.keyword, $options: 'i' } } },
      ];
    }

    // 定义聚合管道
    const pipeline: PipelineStage[] = [
      { $match: query }, // 匹配查询条件
      {
        $facet: {
          meta: [{ $count: 'total' }],
          data: [
            {
              $sort: {
                [sortField]: sortOrder === 'asc' ? 1 : -1,
              },
            },
            { $skip: skip },
            { $limit: pageSize },
          ],
        },
      },
    ];

    type FacetResult = {
      meta: { total: number }[];
      data: ContentsDocument[];
    };

    // 执行聚合查询
    const result = await this.contentModel.aggregate<FacetResult>(pipeline);

    // 解析管道结果
    const [facetResult] = result;
    const total = facetResult.meta[0]?.total || 0;
    const list = await Promise.all(
      facetResult.data.map(async (item) => {
        return await this.findOneById(item._id.toString(), userId);
      })
    );

    return {
      page,
      pageSize,
      totalCount: total,
      totalPage: Math.ceil(total / pageSize),
      sortField,
      sortOrder,
      list,
    };
  }

  /**
   * !: 帖子浏览量自增的异步方法
   * @param id 帖子 ID
   * @returns 帖子
   * @throws HttpException 帖子不存在
   */
  async increaseViewCount(id: string) {
    const content = await this.contentModel.findByIdAndUpdate(
      id,
      {
        $inc: { viewCount: 1 },
      },
      { new: true }
    );
    if (!content?.toJSON()) {
      throw new HttpException('帖子不存在', 404);
    }
    return content.toJSON();
  }

  /**
   * !: 动态点赞或取消点赞帖子
   * @param userId 用户 ID
   * @param contentId 帖子 ID
   * @description
   * 1. 如果点赞关系不存在，则执行点赞操作。
   * 2. 如果点赞关系已存在，则执行取消点赞操作。
   * 3. 使用事务确保操作的原子性。
   * @returns 返回一个布尔值，表示是否成功执行了点赞或取消点赞操作。
   */
  async toggleLikeContent(userId: string, contentId: string): Promise<boolean> {
    // 检查是否已点赞
    const exists = await this.contentsLikeModel.exists({
      user: userId,
      content: contentId,
    });

    if (exists) {
      // 如果已点赞，则执行取消点赞逻辑
      await this.unlikeContentInternal(userId, contentId);
      return false; // 返回 false 表示取消点赞
    } else {
      // 如果未点赞，则执行点赞逻辑
      await this.likeContentInternal(userId, contentId);
      return true; // 返回 true 表示点赞
    }
  }

  /**
   * !: 点赞帖子的内部逻辑
   * @param userId 用户 ID
   * @param contentId 帖子 ID
   * @description
   * 1. 创建新的点赞记录。
   * 2. 更新帖子的点赞数量（likeCount）。
   */
  private async likeContentInternal(
    userId: string,
    contentId: string
  ): Promise<void> {
    // 创建新的点赞记录
    await this.contentsLikeModel.create({
      user: userId,
      content: contentId,
    });

    // 更新帖子的点赞数量（likeCount）
    await this.contentModel.findByIdAndUpdate(contentId, {
      $inc: { likeCount: 1 },
    });

    // 更新用户点赞帖子数量（likeContentsCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { likeContentsCount: 1 },
    });
  }

  /**
   * !: 取消点赞帖子的内部逻辑
   * @param userId 用户 ID
   * @param contentId 帖子 ID
   * @description
   * 1. 删除点赞记录。
   * 2. 更新帖子的点赞数量（likeCount）。
   */
  private async unlikeContentInternal(
    userId: string,
    contentId: string
  ): Promise<void> {
    // 删除点赞记录
    await this.contentsLikeModel.deleteOne({
      user: userId,
      content: contentId,
    });

    // 更新帖子的点赞数量（likeCount）
    await this.contentModel.findByIdAndUpdate(contentId, {
      $inc: { likeCount: -1 },
    });

    // 更新用户点赞帖子数量（likeContentsCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { likeContentsCount: -1 },
    });
  }

  /**
   * !: 动态收藏或取消收藏帖子
   * @param userId 用户 ID
   * @param contentId 帖子 ID
   * @description
   * 1. 如果收藏关系不存在，则执行收藏操作。
   * 2. 如果收藏关系已存在，则执行取消收藏操作。
   * 3. 使用事务确保操作的原子性。
   * @returns 返回一个布尔值，表示是否成功执行了收藏或取消收藏操作。
   */
  async toggleFavoriteContent(
    userId: string,
    contentId: string
  ): Promise<boolean> {
    // 检查是否已收藏
    const exists = await this.contentsFavoriteModel.exists({
      user: userId,
      content: contentId,
    });

    if (exists) {
      // 如果已收藏，则执行取消收藏逻辑
      await this.unfavoriteContentInternal(userId, contentId);
      return false; // 返回 false 表示取消收藏
    } else {
      // 如果未收藏，则执行收藏逻辑
      await this.favoriteContentInternal(userId, contentId);
      return true; // 返回 true 表示收藏
    }
  }

  /**
   * !: 收藏帖子的内部逻辑
   * @param userId 用户 ID
   * @param contentId 帖子 ID
   * @description
   * 1. 创建新的收藏记录。
   * 2. 更新帖子的收藏数量（favoriteCount）。
   */
  private async favoriteContentInternal(
    userId: string,
    contentId: string
  ): Promise<void> {
    // 创建新的收藏记录
    await this.contentsFavoriteModel.create({
      user: userId,
      content: contentId,
    });

    // 更新帖子的收藏数量（favoriteCount）
    await this.contentModel.findByIdAndUpdate(contentId, {
      $inc: { favoriteCount: 1 },
    });

    // 更新用户收藏帖子数量（favoriteContentsCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { favoriteContentsCount: 1 },
    });
  }

  /**
   * !: 取消收藏帖子的内部逻辑
   * @param userId 用户 ID
   * @param contentId 帖子 ID
   * @description
   * 1. 删除收藏记录。
   * 2. 更新帖子的收藏数量（favoriteCount）。
   */
  private async unfavoriteContentInternal(
    userId: string,
    contentId: string
  ): Promise<void> {
    // 删除收藏记录
    await this.contentsFavoriteModel.deleteOne({
      user: userId,
      content: contentId,
    });

    // 更新帖子的收藏数量（favoriteCount）
    await this.contentModel.findByIdAndUpdate(contentId, {
      $inc: { favoriteCount: -1 },
    });

    // 更新用户收藏帖子数量（favoriteContentsCount）
    await this.usersModel.findByIdAndUpdate(userId, {
      $inc: { favoriteContentsCount: -1 },
    });
  }
}
