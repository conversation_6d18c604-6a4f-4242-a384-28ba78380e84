/**
 * RedisModuleOptions 接口定义了配置 Redis 模块时可以使用的选项。
 */
export interface RedisModuleOptions {
  /**
   * 可选属性，Redis 实例的 URL 地址。
   * 如果提供，将忽略 host 和 port 属性。
   */
  url?: string;

  /**
   * 可选属性，Redis 实例的主机地址。
   * 如果未提供 url，则该属性是必需的。
   */
  host?: string;

  /**
   * 可选属性，Redis 实例的端口号。
   * 如果未提供 url，则该属性是必需的。
   */
  port?: number;

  /**
   * 可选属性，用于连接 Redis 实例的密码。
   */
  password?: string;

  /**
   * 可选属性，设置存储在 Redis 中的数据的默认生存时间（TTL）。
   * 单位为秒。
   */
  ttl?: number; // 默认 TTL（可选）
}
