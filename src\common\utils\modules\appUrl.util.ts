/**
 * ?: URL格式化器
 * 用于格式化URL，将IPv6地址[::1]替换为'localhost'
 */
class UrlFormatter {
  private url: string; // 存储URL的私有属性

  // 构造函数，可选参数url，默认为空字符串
  constructor(url: string = '') {
    this.url = url; // 初始化url属性
  }

  // 格式化URL的方法，接受一个url字符串作为参数
  public formatUrl(url: string, domain?: string): string {
    // 如果URL包含'[::1]'，这意味着是本地IPv6地址，替换为'localhost'
    if (url.includes('[::1]')) {
      // 如果传入自定义域名，则替换URL中的域名
      if (domain) {
        return (this.url = url.replace('[::1]', domain));
      }
      return (this.url = url.replace('[::1]', 'localhost'));
    }

    // 如果URL不包含'[::1]'，则直接返回该URL
    return (this.url = url);
  }

  // 获取当前格式化后的URL的方法
  public getUrl(): string {
    return this.url; // 返回url属性的值
  }
}

/**
 * ?: 导出实例
 * 导出一个UrlFormatter类的实例，用于格式化URL
 */
export const appUrl = new UrlFormatter();
