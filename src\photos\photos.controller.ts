import {
  Controller,
  Post,
  Body,
  Req,
  Get,
  Param,
  Put,
  Delete,
  Query,
} from '@nestjs/common';
import { PhotosService } from './photos.service';
import { CreatePhotoDto } from './dtos/create-photo.dto';
import { Request } from 'express';
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { Public } from 'src/common/decorator/public.decorator';
import { PhotoPublicInfoDto } from './filters/photo-public.dto';
import { plainToClass } from 'class-transformer';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';
import { formatResponse } from 'src/common/utils';
import { PhotoProfileInfoDto } from './filters/photo-profile.dto';
import { UpdatePhotoDto } from './dtos/update-photo.dto';
import { SearchAllPhotoDto } from './dtos/search-photo.dto';
import { PhotoListInfoDto } from './filters/photo-list.dto';

@Controller('photo')
export class PhotosController {
  constructor(private readonly photoService: PhotosService) {}

  @ApiOperation({ summary: '创建一个图片-用户路由' })
  @ApiOkResponse({ description: '创建成功返回图片信息' })
  // !: 定义POST请求的路由，用于创建图片
  @Post()
  async createPhoto(
    @Body() createPhotoDto: CreatePhotoDto,
    @Req() request: Request
  ) {
    const { id } = request.user;
    const photo = await this.photoService.create(id, createPhotoDto);

    return formatResponse(
      HttpStatusEnum.OK,
      '创建成功',
      plainToClass(PhotoProfileInfoDto, photo, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '获取用户自己上传的图片信息-用户路由' })
  @ApiOkResponse({ description: '获取成功返回图片信息' })
  // !: 定义GET请求的路由，用于获取用户自己上传的图片信息
  @Get(':id')
  async findPhoto(@Param('id') id: string, @Req() req: Request) {
    const photo = await this.photoService.findOneById(id, req.user.id);

    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      plainToClass(PhotoProfileInfoDto, photo, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '更新图片信息-用户路由' })
  @ApiOkResponse({ description: '更新成功返回图片信息' })
  // !: 定义PUT请求的路由，用于更新图片信息
  @Put(':id')
  async updatePhoto(
    @Body() updatePhotoDto: UpdatePhotoDto,
    @Param('id') id: string,
    @Req() req: Request
  ) {
    const photo = await this.photoService.update(
      req.user.id,
      id,
      updatePhotoDto
    );

    return formatResponse(
      HttpStatusEnum.OK,
      '更新成功',
      plainToClass(PhotoProfileInfoDto, photo, {
        excludeExtraneousValues: true,
      })
    );
  }

  @ApiOperation({ summary: '删除图片-用户路由' })
  @ApiOkResponse({ description: '删除成功' })
  // !: 定义DELETE请求的路由，用于删除图片
  @Delete(':id')
  async deletePhoto(@Param('id') id: string, @Req() req: Request) {
    await this.photoService.delete(req.user.id, id);
    return formatResponse(HttpStatusEnum.OK, '删除成功');
  }

  @ApiOperation({ summary: '全部用户获取全部图片列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回图片列表' })
  // !: 定义GET请求的路由，用于获取全部图片列表
  @Get('public/all')
  @Public() // 定义路由为公开路由，不用登录即可访问
  async findUserPhotos(
    @Query() searchAllPhotoDto: SearchAllPhotoDto,
    @Req() req: Request
  ) {
    const data = await this.photoService.findList(
      searchAllPhotoDto,
      req.user ? req.user.id : undefined
    );
    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((photo) =>
        plainToClass(PhotoListInfoDto, photo, {
          excludeExtraneousValues: true,
        })
      ),
    });
  }

  @ApiOperation({ summary: '获取指定图片信息-公开路由' })
  @ApiOkResponse({ description: '获取成功返回图片信息' })
  // !: 定义GET请求的路由，用于获取指定id的图片信息
  @Get('public/:id')
  @Public() // 定义路由为公开路由，不用登录即可访问
  async findPhotoList(@Param('id') id: string, @Req() req: Request) {
    const photo = await this.photoService.findOneById(
      id,
      req.user ? req.user.id : undefined
    );
    this.photoService.increaseVisitors(id);
    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      plainToClass(PhotoPublicInfoDto, photo, { excludeExtraneousValues: true })
    );
  }

  @ApiOperation({ summary: '全部用户获取指定id用户上传的图片列表-公开路由' })
  @ApiOkResponse({ description: '获取成功返回图片列表' })
  // !: 定义GET请求的路由，用于获取用户上传的图片列表
  @Get('public/all/:uid')
  @Public() // 定义路由为公开路由，不用登录即可访问
  async findPhotosListByUser(
    @Query() searchAllPhotoDto: SearchAllPhotoDto,
    @Param('uid') uid: string,
    @Req() req: Request
  ) {
    const data = await this.photoService.findList(
      {
        uid,
        ...searchAllPhotoDto,
      },
      req.user ? req.user.id : undefined
    );
    return formatResponse(HttpStatusEnum.OK, '获取成功', {
      ...data,
      list: data.list.map((photo) =>
        plainToClass(PhotoPublicInfoDto, photo, {
          excludeExtraneousValues: true,
        })
      ),
    });
  }

  @ApiOperation({ summary: '获取图片名以及下载次数自增-用户路由' })
  @ApiOkResponse({ description: '获取成功返回图片名' })
  // !: 定义GET请求的路由，用于获取图片名以及下载次数自增
  @Get('public/download/:id')
  async increaseDownloadCount(@Param('id') id: string) {
    const photo = await this.photoService.findOneById(id);
    this.photoService.increaseDownloads(id);
    return formatResponse(
      HttpStatusEnum.OK,
      '获取成功',
      photo.downloadFilename
    );
  }

  @ApiOperation({ summary: '点赞或取消点赞图片-用户路由' })
  @ApiOkResponse({ description: '点赞或取消点赞成功' })
  // !: 定义POST请求处理方法，用于点赞或取消点赞图片
  @Post('like/:id')
  async likeContent(@Param('id') id: string, @Req() req: Request) {
    const { id: userId } = req.user;
    const content = await this.photoService.toggleLikePhoto(userId, id);
    return formatResponse(
      HttpStatusEnum.OK,
      content ? '点赞成功' : '取消点赞成功'
    );
  }

  @ApiOperation({ summary: '收藏或取消收藏图片-用户路由' })
  @ApiOkResponse({ description: '收藏或取消收藏成功' })
  // !: 定义POST请求处理方法，用于收藏或取消收藏图片
  @Post('favorite/:id')
  async favoriteContent(@Param('id') id: string, @Req() req: Request) {
    const { id: userId } = req.user;
    const content = await this.photoService.toggleFavoritePhoto(userId, id);
    return formatResponse(
      HttpStatusEnum.OK,
      content ? '收藏成功' : '取消收藏成功'
    );
  }
}
