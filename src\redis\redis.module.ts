import { DynamicModule, Logger, Module, Provider } from '@nestjs/common';
import { createClient } from 'redis';
import { RedisModuleOptions } from './redis.interface';
import {
  REDIS_DATA_CLIENT,
  REDIS_PUBSUB_CLIENT,
  REDIS_MODULE_OPTIONS,
} from './redis.decorators';
import { RedisService } from './redis.service';

const createRedisClient = (
  options: RedisModuleOptions,
  clientType: 'data' | 'pubsub'
) => {
  return createClient({
    url: options.url,
    socket: {
      host: options.host,
      port: options.port,
      reconnectStrategy: (attempts) => {
        const maxRetries = 5;
        if (attempts >= maxRetries) {
          Logger.error(
            `${clientType} Redis 连接失败，超过最大重试次数`,
            'Redis'
          );
          return false;
        }
        return Math.min(attempts * 100, 3000);
      },
    },
    password: options.password,
  });
};

@Module({})
export class RedisModule {
  static forRoot(options: RedisModuleOptions): DynamicModule {
    const providers: Provider[] = [
      {
        provide: REDIS_MODULE_OPTIONS,
        useValue: options,
      },
      // 数据操作客户端
      {
        provide: REDIS_DATA_CLIENT,
        useFactory: async () => {
          const client = createRedisClient(options, 'data');
          this.setupClientEvents(client, 'Data');
          await client.connect();
          return client;
        },
      },
      // 发布订阅客户端
      {
        provide: REDIS_PUBSUB_CLIENT,
        useFactory: async () => {
          const client = createRedisClient(options, 'pubsub');
          this.setupClientEvents(client, 'PubSub');
          await client.connect();
          return client;
        },
      },
      RedisService,
    ];

    return {
      module: RedisModule,
      global: true,
      providers,
      exports: [REDIS_DATA_CLIENT, REDIS_PUBSUB_CLIENT, RedisService],
    };
  }

  private static setupClientEvents(client: any, type: string) {
    /* eslint-disable */
    client.on('error', (err: Error) => {
      Logger.error(`${type} redis 连接出错: ${err.message}`, 'Redis');
    });

    client.on('ready', () => {
      Logger.log(`${type} redis 缓存池已就绪`, 'Redis');
    });

    client.on('end', () => {
      Logger.warn(`${type} redis 缓存池已断开`, 'Redis');
    });
    /* eslint-enable */
  }
}
