import { Expose } from 'class-transformer';

export class UserInfoDto {
  @Expose()
  uid: number; // 用户唯一ID
  @Expose()
  avatar: string; // 头像
  @Expose()
  nickname: string; // 昵称
  @Expose()
  bio: string; // 简介
  @Expose()
  visitedCount: number; // 访问次数
  @Expose()
  failedAttempts: number; // 登录失败次数
  @Expose()
  status: number; // 用户状态
  @Expose()
  riskLevel: number; // 风险等级
  @Expose()
  isDeleted: boolean; // 是否已删除
  @Expose()
  registerIP: string; // 注册IP
  @Expose()
  lastLoginIP: string; // 最后登录IP
  @Expose()
  createTime: Date; // 注册时间
  @Expose()
  updateTime: Date; // 更新时间
  @Expose()
  lastLoginTime: Date; // 最后登录时间
  @Expose()
  lockUntil: Date; // 锁定截止时间
}
