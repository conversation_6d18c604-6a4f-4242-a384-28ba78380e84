<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>邪王真眼对话系统</title>
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=M+PLUS+Rounded+1c:400,700&display=swap"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'M PLUS Rounded 1c', 'Microsoft YaHei', sans-serif;
        background: linear-gradient(135deg, #2b0a3d 0%, #4a154b 100%);
        margin: 0;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .chat-container {
        width: 80%;
        margin: 0 auto;
        background: rgba(29, 15, 39, 0.95);
        border-radius: 20px;
        box-shadow: 0 0 30px rgba(188, 108, 217, 0.3);
        border: 2px solid #bc6cd9;
        position: relative;
        overflow: hidden;
      }

      @keyframes starfield {
        0% {
          transform: rotate(45deg) translateY(0);
        }
        100% {
          transform: rotate(45deg) translateY(-150px);
        }
      }

      .chat-header {
        text-align: center;
        font-size: 2.2rem;
        padding: 25px 0;
        color: #ff79ae;
        text-shadow: 2px 2px 0 #bc6cd9;
        position: relative;
        background: linear-gradient(
          90deg,
          rgba(92, 37, 141, 0.8) 0%,
          rgba(188, 108, 217, 0.8) 50%,
          rgba(92, 37, 141, 0.8) 100%
        );
        border-bottom: 3px solid #ff79ae;
        letter-spacing: 3px;
      }

      .chat-box {
        flex: 1;
        overflow-y: auto;
        padding: 25px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        height: 60vh;
      }

      .chat-box::-webkit-scrollbar {
        width: 10px;
        background: rgba(43, 10, 61, 0.5);
        border-radius: 10px;
      }

      .chat-box::-webkit-scrollbar-track {
        background: transparent;
        margin: 5px 0;
      }

      .chat-box::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #bc6cd9 30%, #ff79ae 70%);
        border-radius: 10px;
        border: 2px solid #ffd700;
        box-shadow:
          0 0 8px rgba(255, 121, 174, 0.5),
          inset 0 0 5px rgba(255, 215, 0, 0.3);
        position: relative;
      }

      .chat-box::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(45deg, #ff79ae 30%, #bc6cd9 70%);
        box-shadow:
          0 0 15px rgba(255, 121, 174, 0.8),
          inset 0 0 8px rgba(255, 215, 0, 0.5);
      }

      /* Firefox兼容 */
      .chat-box {
        scrollbar-width: thin;
        scrollbar-color: #bc6cd9 rgba(43, 10, 61, 0.5);
      }

      .message-row {
        display: flex;
        align-items: flex-end;
        gap: 15px;
        position: relative;
      }

      .user-row {
        flex-direction: row-reverse;
      }

      .message {
        max-width: 75%;
        padding: 8px 10px;
        border-radius: 20px;
        font-size: 17px;
        line-height: 1.7;
        word-break: break-word;
        position: relative;
        transition: transform 0.3s ease;
      }

      .message:hover {
        transform: scale(1.02);
      }

      .user-message {
        background: #ff79ae;
        color: #2b0a3d;
        border: 2px solid #ff4d9e;
        border-bottom-right-radius: 5px;
        box-shadow: 3px 3px 0 #bc6cd9;
      }

      .ai-message {
        background: #bc6cd9;
        color: #fff;
        border: 2px solid #9a4dbf;
        border-bottom-left-radius: 5px;
        box-shadow: -3px 3px 0 #ff79ae;
      }

      .avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        flex-shrink: 0;
        user-select: none;
        border: 2px solid;
        background-size: cover;
      }

      .user-avatar {
        background: url('https://file.sixflower.top/images/img-17512039738822z600w.png?width=1600')
          no-repeat center center;
        background-size: cover;
        border-color: #ff4d9e;
      }

      .ai-avatar {
        background: url('https://file.sixflower.top/images/img-17512039738825oq5jn.png?width=1600')
          no-repeat center center;
        background-size: cover;
        border-color: #9a4dbf;
      }

      .input-container {
        display: flex;
        gap: 15px;
        padding: 20px;
        background: rgba(43, 10, 61, 0.9);
        border-top: 2px solid #bc6cd9;
      }

      #user-input {
        flex: 1;
        padding: 16px;
        border: 2px solid #ff79ae;
        border-radius: 12px;
        font-size: 16px;
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
        outline: none;
        transition: all 0.3s;
      }

      #user-input:focus {
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 15px rgba(255, 121, 174, 0.5);
      }

      button {
        padding: 16px 35px;
        background: linear-gradient(45deg, #ff79ae 0%, #bc6cd9 100%);
        border: 2px solid #fff;
        border-radius: 12px;
        color: #fff;
        font-weight: bold;
        text-shadow: 1px 1px 0 #9a4dbf;
        cursor: pointer;
        transition: all 0.3s;
        position: relative;
        overflow: hidden;
      }

      button::before {
        content: '★';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0;
        transition: all 0.3s;
      }

      button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(188, 108, 217, 0.4);
      }

      button:hover::before {
        right: 10px;
        opacity: 1;
      }

      .loading {
        position: absolute;
        bottom: 80px;
        right: 30px;
        padding: 8px 20px;
        background: rgba(188, 108, 217, 0.9);
        border-radius: 15px;
        color: #ffd700;
        font-size: 14px;
        border: 2px solid #ff79ae;
        box-shadow: 0 0 15px rgba(188, 108, 217, 0.5);
        display: none;
        z-index: 100;
        backdrop-filter: blur(3px);
        transform: translateY(20px);
        animation: float 2s ease-in-out infinite;
      }

      .loading::after {
        content: '';
        position: absolute;
        top: -6px;
        right: 20px;
        width: 12px;
        height: 12px;
        background: #ff79ae;
        transform: rotate(45deg);
        border: 2px solid #ffd700;
      }

      @keyframes float {
        0% {
          transform: translateY(20px) rotate(2deg);
        }
        50% {
          transform: translateY(15px) rotate(-2deg);
        }
        100% {
          transform: translateY(20px) rotate(2deg);
        }
      }

      @keyframes sparkle {
        0% {
          opacity: 0.3;
        }
        50% {
          opacity: 1;
          text-shadow: 0 0 10px #ff79ae;
        }
        100% {
          opacity: 0.3;
        }
      }

      .markdown-body {
        color: #fff !important;
      }

      .markdown-body code {
        background: rgba(255, 121, 174, 0.2);
        color: #ffc8e0;
        border: 1px solid #ff79ae;
      }

      .markdown-body pre {
        background: rgba(43, 10, 61, 0.9);
        border: 2px solid #bc6cd9;
        color: #ffc8e0;
      }

      .think-msg {
        color: #ffd700 !important;
        text-shadow: 0 0 8px rgba(255, 215, 0, 0.5);
        border: 1px solid #ffd700;
        background: rgba(43, 10, 61, 0.9) !important;
      }

      /* 新增标题特效 */
      .glitch-text {
        position: relative;
        font-family: 'Courier New', monospace; /* 强制等宽字体 */
        letter-spacing: -3px; /* 压缩字符间距 */
      }

      .glitch-text::before,
      .glitch-text::after {
        content: attr(data-text);
        position: absolute;
        top: 0;
        width: 100%;
        background: transparent;
      }

      /* 副标题符号校准 */
      .subtitle {
        position: relative;
        font-size: 1rem;
        padding-left: 1.2em; /* 为方块预留空间 */
      }

      /* 系统状态指示器 */
      .system-msg {
        position: absolute;
        top: 3px;
        right: 20px;
        font-size: 0.8rem;
        background: rgba(43, 10, 61, 0.7);
        padding: 8px 15px;
        border-radius: 12px;
        border: 1px solid #ff79ae;
      }
      .rainbow-text {
        background: linear-gradient(45deg, #ff79ae, #bc6cd9, #ffd700);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      /* 输入框动态placeholder */
      #user-input[data-placeholder-cycle]::placeholder {
        animation: placeholderCycle 9s infinite;
        opacity: 0.7;
      }

      @media only screen and (max-width: 768px) {
        .chat-header {
          font-size: 1.8rem; /* 减小标题字体大小以适应手机屏幕 */
        }

        .message-row {
          flex-wrap: wrap; /* 允许子元素换行以适应手机屏幕 */
        }

        .message {
          max-width: 90%; /* 增加消息框的宽度 */
          font-size: 15px; /* 减小消息字体大小以适应手机屏幕 */
        }

        .input-container {
          flex-wrap: wrap; /* 允许子元素换行以适应手机屏幕 */
          gap: 10px; /* 减小间隔以适应手机屏幕 */
        }

        #user-input {
          font-size: 14px; /* 减小输入框字体大小以适应手机屏幕 */
        }

        button {
          padding: 14px 30px; /* 减小按钮内边距以适应手机屏幕 */
        }

        .subtitle {
          position: relative;
          font-size: 0.9rem; /* 减小字体大小以适应手机屏幕 */
          padding-left: 1em; /* 调整为1em以适应手机屏幕 */
        }

        /* 系统状态指示器 */
        .system-msg {
          position: absolute;
          top: 3px;
          right: 10px; /* 调整到右侧10px以适应手机屏幕 */
          font-size: 0.7rem; /* 减小字体大小以适应手机屏幕 */
          background: rgba(43, 10, 61, 0.7);
          padding: 6px 10px; /* 减小内边距 */
          border-radius: 10px; /* 减小边框半径 */
          border: 1px solid #ff79ae;
        }

        .chat-container {
          width: 95%; /* 增加宽度以适应手机屏幕 */
          margin: 0 auto;
          background: rgba(29, 15, 39, 0.95);
          border-radius: 15px; /* 增加边框半径 */
          box-shadow: 0 0 20px rgba(188, 108, 217, 0.3);
          border: 2px solid #bc6cd9;
          position: relative;
          overflow: hidden;
        }
      }
    </style>
  </head>
  <body>
    <div class="chat-container">
      <!-- 修改标题部分 -->
      <div class="chat-header">
        <div class="glitch-text">✦ 不可视境界线接触协议·ver六花 ✦</div>
        <div class="subtitle">暗号名称：<em>Schwarz Sechs Blume</em></div>

        <!-- 增加状态提示气泡 -->
        <div class="system-msg">
          <span id="protocol-status"
            >■ 当前契约稳定率：<span class="rainbow-text">98.7%</span></span
          >
          <span id="dark-matter"
            >☄ 黑暗物质浓度：<span class="pulse">3.14μSv</span></span
          >
        </div>
      </div>

      <div class="chat-box" id="chat-box"></div>
      <div class="loading" id="loading">契约执行中...</div>
      <div class="input-container">
        <!-- 输入框提示文本改造 -->
        <input
          type="text"
          id="user-input"
          placeholder="[需加密传输] 请输入与邪王真眼的共鸣波长..."
          data-placeholder-cycle="[需加密传输] 请输入暗号化思念体;/[警告] 不可视境界线观测中...;/[需加密传输] 请输入圣遗物解析指令"
        />
        <button onclick="sendMessage()">发动！</button>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
      marked.setOptions({
        breaks: true,
      });

      // 动态状态指示
      setInterval(() => {
        document.getElementById('protocol-status').innerHTML =
          `■ 当前契约稳定率：<span class="rainbow-text">${Math.floor(Math.random() * 20 + 80)}.${Math.floor(Math.random() * 10)}%</span>`;

        document.getElementById('dark-matter').innerHTML =
          `☄ 黑暗物质浓度：<span class="pulse">${(Math.random() * 5).toFixed(2)}μSv</span>`;
      }, 3000);

      // placeholder循环
      const placeholders = document
        .getElementById('user-input')
        .getAttribute('data-placeholder-cycle')
        .split(';');
      let phIndex = 0;
      setInterval(() => {
        document.getElementById('user-input').placeholder =
          placeholders[phIndex];
        phIndex = (phIndex + 1) % placeholders.length;
      }, 2500);

      const chatBox = document.getElementById('chat-box');
      const userInput = document.getElementById('user-input');
      const loading = document.getElementById('loading');

      let isProcessing = false;

      function renderThinkMsg(content) {
        return `<span class="think-msg">✦ ${content} ✦</span>`;
      }

      function renderAIContent(think, markdown) {
        let html = '';
        if (think) html += renderThinkMsg(think);
        if (markdown)
          html += `<div class="markdown-body">${marked.parse(markdown)}</div>`;
        return html;
      }

      function addMessage(content, isUser) {
        const row = document.createElement('div');
        row.className = 'message-row ' + (isUser ? 'user-row' : '');
        const avatar = document.createElement('div');
        avatar.className = 'avatar ' + (isUser ? 'user-avatar' : 'ai-avatar');
        avatar.textContent = isUser ? '' : '';
        const messageDiv = document.createElement('div');
        messageDiv.className =
          'message ' + (isUser ? 'user-message' : 'ai-message');
        messageDiv.innerHTML = content;
        row.appendChild(avatar);
        row.appendChild(messageDiv);
        chatBox.appendChild(row);
        chatBox.scrollTop = chatBox.scrollHeight;
        return messageDiv;
      }

      async function sendMessage() {
        const message = userInput.value.trim();
        if (!message || isProcessing) return; // 阻止空内容或处理中的请求

        addMessage(message, true);
        isProcessing = true;
        userInput.disabled = true;
        document.querySelector('button').style.opacity = '0.6';
        userInput.value = '';
        loading.style.display = 'block';

        const aiDiv = addMessage('...', false);
        let mode = 'normal';
        let thinkContent = '';
        let markdownContent = '';
        let aiContent = '';

        try {
          const response = await fetch('/chat/stream', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': '123456',
            },
            body: JSON.stringify({ message }),
          });

          const reader = response.body.getReader();
          const decoder = new TextDecoder('utf-8');
          let done = false;

          while (!done) {
            const { value, done: doneReading } = await reader.read();
            done = doneReading;
            if (value) {
              const chunk = decoder.decode(value);
              chunk.split('\n\n').forEach((line) => {
                if (line.startsWith('data: ')) {
                  let content = line.slice(6);
                  if (content === '[END]') {
                    // 结束标记时解除锁定
                    isProcessing = false;
                    userInput.disabled = false;
                    document.querySelector('button').style.opacity = '1';
                    return;
                  }
                  while (content.length > 0) {
                    if (mode === 'normal') {
                      const thinkStart = content.indexOf('<think>');
                      if (thinkStart !== -1) {
                        mode = 'think';
                        content = content.slice(thinkStart + 7);
                        thinkContent = '';
                        markdownContent = '';
                        aiContent = '';
                      } else {
                        break;
                      }
                    } else if (mode === 'think') {
                      const thinkEnd = content.indexOf('</think>');
                      if (thinkEnd !== -1) {
                        thinkContent += content.slice(0, thinkEnd);
                        aiContent = renderAIContent(
                          thinkContent,
                          markdownContent
                        );
                        aiDiv.innerHTML = aiContent;
                        mode = 'markdown';
                        content = content.slice(thinkEnd + 8);
                      } else {
                        thinkContent += content;
                        aiContent = renderAIContent(
                          thinkContent,
                          markdownContent
                        );
                        aiDiv.innerHTML = aiContent;
                        content = '';
                      }
                    } else if (mode === 'markdown') {
                      markdownContent += content;
                      aiContent = renderAIContent(
                        thinkContent,
                        markdownContent
                      );
                      aiDiv.innerHTML = aiContent;
                      content = '';
                    }
                    requestAnimationFrame(() => {
                      chatBox.scrollTop = chatBox.scrollHeight;
                    });
                  }
                }
              });
            }
          }
        } catch (error) {
          isProcessing = false;
          userInput.disabled = false;
          document.querySelector('button').style.opacity = '1';
          aiDiv.innerHTML =
            '<span style="color:#ff79ae">✦ 黑暗力量不稳定，请重新构筑魔法阵！✦</span>';
        } finally {
          loading.style.display = 'none';
          // 最终确保解除锁定
          isProcessing = false;
          userInput.disabled = false;
          document.querySelector('button').style.opacity = '1';
        }
      }

      userInput.addEventListener('keypress', function (e) {
        if (e.key === 'Enter') sendMessage();
      });

      document
        .querySelector('button')
        .addEventListener('mouseover', function () {
          if (isProcessing) {
            this.title = '邪王真眼正在解析中，请等待契约完成！';
          }
        });
    </script>
  </body>
</html>
