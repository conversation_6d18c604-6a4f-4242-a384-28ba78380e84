import { Expose } from 'class-transformer';

// 用户个人资料页面返回数据DTO
export class UserProfileInfoDto {
  @Expose()
  uid: number; // 用户唯一ID
  @Expose()
  avatar: string; // 头像
  @Expose()
  background: string; // 背景图
  @Expose()
  nickname: string; // 昵称
  @Expose()
  gender: string; // 性别
  @Expose()
  birthday: Date; // 生日
  @Expose()
  bio: string; // 简介
  @Expose()
  totalDownloads: string; // 总下载量
  @Expose()
  followersCount: number; // 关注数
  @Expose()
  followingsCount: number; // 粉丝数
  @Expose()
  visitedCount: number; // 被访问量
  @Expose()
  photosCount: number; // 照片数
  @Expose()
  contentsCount: number; // 文章数
  @Expose()
  likePhotosCount: number; // 喜欢的照片数
  @Expose()
  favoritePhotosCount: number; // 收藏的照片数
  @Expose()
  likeContentsCount: number; // 喜欢的文章数
  @Expose()
  favoriteContentsCount: number; // 收藏的文章数
  @Expose()
  email: string; // 邮箱（可选，需用户确认权限）
  @Expose()
  phone: string; // 手机号（可选，需用户确认权限）
  @Expose()
  socialLogins: Array<{ provider: string; providerId: string }>; // 第三方登录信息
  @Expose()
  createTime: Date; // 注册时间
}
