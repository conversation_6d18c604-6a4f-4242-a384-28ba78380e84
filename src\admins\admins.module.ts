import { Modu<PERSON> } from '@nestjs/common';
import { AdminsService } from './admins.service';
import { AdminsController } from './admins.controller';
import { UsersModule } from 'src/users/users.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Users, UsersSchema } from 'src/users/schemas/users.schema';
import {
  UsersSetting,
  UsersSettingSchema,
} from 'src/users/schemas/users-setting.schema';
import { RedisModule } from 'src/redis/redis.module';

@Module({
  imports: [
    UsersModule,
    RedisModule,
    MongooseModule.forFeature([
      { name: Users.name, schema: UsersSchema },
      { name: UsersSetting.name, schema: UsersSettingSchema },
    ]),
  ],
  controllers: [AdminsController],
  providers: [AdminsService],
  exports: [AdminsService],
})
export class AdminsModule {}
