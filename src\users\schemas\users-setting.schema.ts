import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';

export type UsersSettingDocument = HydratedDocument<UsersSetting>;

@Schema({
  // 禁用 __v 版本字段
  versionKey: false,
  toJSON: {
    virtuals: true,
    transform: (_, ret: UsersSettingDocument) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-unsafe-assignment
      const { _id, id, user, ...rest } = ret;
      return {
        ...rest,
      };
    },
  },
})
export class UsersSetting {
  /** 用户ID */
  @Prop({ type: String, ref: 'Users', index: true, required: true })
  user: string;

  /** 是否显示关注列表 */
  @Prop({ default: true, required: true })
  showFollowList: boolean;

  /** 是否显示粉丝列表 */
  @Prop({ default: true, required: true })
  showFansList: boolean;

  /** 是否显示收藏的图片列表 */
  @Prop({ default: true, required: true })
  showFavoritePhotoList: boolean;

  /** 是否显示点赞的图片列表 */
  @Prop({ default: true, required: true })
  showLikePhotoList: boolean;

  /** 是否显示收藏的帖子列表 */
  @Prop({ default: true, required: true })
  showFavoriteContentList: boolean;

  /** 是否显示点赞的帖子列表 */
  @Prop({ default: true, required: true })
  showLikeContentList: boolean;

  /** 是否显示我上传的图片列表 */
  @Prop({ default: true, required: true })
  showMyPhotoList: boolean;

  /** 是否显示我上传的帖子列表 */
  @Prop({ default: true, required: true })
  showMyContentList: boolean;
}

// 使用SchemaFactory创建Schema实例
export const UsersSettingSchema = SchemaFactory.createForClass(UsersSetting);
