{"apikeys": [""], "database": {"host": "", "port": 27017, "name": "", "username": "", "password": ""}, "redis": {"host": "", "port": 26739, "password": ""}, "jwt": {"secret": "", "expiresIn": "1h"}, "email": {"appName": "次元回廊", "supportEmail": "<EMAIL>", "unsubscribeUrl": "http://localhost:3000/users/unsubscribe", "host": "smtp.163.com", "port": 465, "secure": true, "user": "<EMAIL>", "pass": "MNhf9ER9KDWsmFSy", "from": "<EMAIL>"}, "twilio": {"accountSid": "ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX", "authToken": "your_auth_token", "phone": "+**********"}, "dysmsapi": {"accessKeyId": "your_access_key_id", "accessKeySecret": "your_access_key_secret", "signName": "your_sign_name", "templateCode": "your_template_code"}, "wechat": {"appID": "your_app_id", "appSecret": "your_app_secret", "callbackUrl": "your_callback_url"}, "swagger": {"title": "次元回廊 服务器 API", "description": "小鸟游六花风格的次元回廊服务器 API 文档 模拟邮件接收地址:https://ethereal.email/messages", "version": "1.0.0", "path": "/api-docs"}}