import { Request, Response, NextFunction } from 'express';
import * as config from 'config';
import { HttpException } from '@nestjs/common';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';

/**
 * ?: API密钥验证中间件
 * @param whiteListRoutes 白名单路由,禁用整个路由的API密钥验证
 * @returns 中间件函数
 */
export const apiKeyAuth = (whiteListRoutes: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // 读取配置文件内的API密钥, 并转换为Set, 防止重复
    const validKeys = new Set(config.get<[string]>('apikeys'));

    // 从请求头中读取客户端提供的API密钥
    const clientKey = req.headers['x-api-key'];

    // 检查请求路径是否在白名单中
    if (
      whiteListRoutes.some((router) =>
        req.path.startsWith(router[0] != '/' ? `/${router}` : router)
      )
    ) {
      console.log(`请求路径 ${req.path} 在白名单中，跳过API密钥验证`);

      return next();
    }

    // 如果未配置API密钥，则不进行验证
    if (validKeys.size === 0) {
      console.warn('警告：未配置API密钥，禁用安全验证');
      return next();
    }

    // 如果客户端未提供API密钥，则返回401错误
    if (!clientKey) {
      throw new HttpException('未提供API密钥', HttpStatusEnum.UNAUTHORIZED);
    }

    // 如果客户端提供的API密钥不在配置的API密钥中，则返回401错误
    if (validKeys.has(clientKey as string)) {
      next();
    } else {
      throw new HttpException('无效的API密钥', HttpStatusEnum.UNAUTHORIZED);
    }
  };
};
