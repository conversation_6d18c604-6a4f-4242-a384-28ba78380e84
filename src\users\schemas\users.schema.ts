import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'; // 导入NestJS Mongoose的装饰器和工厂
import { HydratedDocument } from 'mongoose'; // 导入Mongoose库及其HydratedDocument类型

import {
  UserGenderEnum,
  UserRoleEnum,
  UserStatusEnum,
} from 'src/shared/enums/User.enum';

// 定义User文档类型，继承自HydratedDocument，包含User类的所有字段
export type UsersDocument = HydratedDocument<Users>;

// 使用@Schema装饰器定义User类为Mongoose的Schema
@Schema({
  timestamps: { createdAt: 'createTime', updatedAt: 'updateTime' },
  // 禁用 __v 版本字段
  versionKey: false,
  toJSON: {
    virtuals: true,
    transform: (_, ret: UsersDocument) => {
      if (ret.isDeleted) {
        return null;
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-unsafe-assignment
      const { _id, id, ...rest } = ret;
      return {
        id: _id.toString(),
        ...rest,
      };
    },
  },
})
export class Users {
  /** 用户唯一标识符 */
  @Prop({})
  id: string;

  /** 用户ID，由系统生成，不可修改 */
  @Prop({
    required: [true, '用户ID不能为空'],
    unique: [true, '用户ID已存在'],
    index: true,
    default: '000000',
  })
  uid: string;

  /** 用户头像 */
  @Prop({
    trim: true,
    default: `https://file.sixflower.top/images/img-1751204225972fvug1a.jpg`,
  })
  avatar: string;

  /** 主页背景图 */
  @Prop({
    trim: true,
    default: `https://file.sixflower.top/images/img-1751204225972pnyyer.jpg`,
  })
  background: string;

  /** 用户昵称，初始由系统生成，可修改 */
  @Prop({
    required: [true, '昵称不能为空'],
    trim: true,
    minlength: [1, '昵称长度不能少于1个字符'],
    maxlength: [15, '昵称长度不能超过15个字符'],
  })
  nickname: string;

  /** 用户密码，存入前需加密，加密前长度需大于等于8，最大长度不超过20 可修改 */
  @Prop({
    required: [true, '密码不能为空'],
  })
  password: string;

  /** 用户邮箱，唯一，可修改 */
  @Prop({
    unique: [true, '邮箱已被注册'],
    trim: true,
    lowercase: true,
    match: [
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      '邮箱格式不正确',
    ],
  })
  email: string;

  /** 用户手机号码，唯一，可修改 */
  @Prop({
    trim: true,
    unique: [true, '手机号码已被注册'],
    sparse: true,
    match: [
      /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/,
      '手机号码格式不正确',
    ],
  })
  phone: string;

  /** 用户性别 默认隐藏 可修改 enum: ['male', 'female', 'hidden'] */
  @Prop({
    enum: UserGenderEnum,
    default: UserGenderEnum.Hidden,
  })
  gender: string;

  /** 用户生日，可修改 */
  @Prop({ default: Date.now })
  birthday: Date;

  /** 用户简介，可修改 最大长度100 */
  @Prop({
    trim: true,
    maxlength: 300,
    default: '这个人很懒，什么都没留下 ~~~',
  })
  bio: string;

  /** 关注数量 */
  @Prop({ default: 0 })
  followersCount: number;

  /** 粉丝数量 */
  @Prop({ default: 0 })
  followingsCount: number;

  /** 图片数量 */
  @Prop({ default: 0 })
  photosCount: number;

  /** 帖子数量 */
  @Prop({ default: 0 })
  contentsCount: number;

  /** 点赞图片数量 */
  @Prop({ default: 0 })
  likePhotosCount: number;

  /** 收藏图片数量 */
  @Prop({ default: 0 })
  favoritePhotosCount: number;

  /** 点赞帖子数量 */
  @Prop({ default: 0 })
  likeContentsCount: number;

  /** 收藏帖子数量 */
  @Prop({ default: 0 })
  favoriteContentsCount: number;

  /** 被访问次数 */
  @Prop({ default: 0, min: 0 })
  visitedCount: number;

  /** 发布图片总下载量 */
  @Prop({ default: 0, min: 0 })
  totalDownloads: number;

  /** 用户权限 */
  @Prop({
    required: [true, '用户权限不能为空'],
    enum: UserRoleEnum,
    default: UserRoleEnum.User,
  })
  role: UserRole;

  /** 第三方登录信息 */
  @Prop({
    type: [{ provider: String, providerId: String }],
    default: [],
  })
  socialLogins: Array<{ provider: string; providerId: string }>;

  /** 登陆失败次数 */
  @Prop({ type: Number, default: 0, min: 0 })
  failedAttempts: number;

  /** 用户状态 */
  @Prop({
    enum: UserStatusEnum,
    default: UserStatusEnum.Active,
  })
  status: string;

  /** 用户风险等级 */
  @Prop({ default: 0 })
  riskLevel: number;

  /** 用户是否已删除 */
  @Prop({ default: false })
  isDeleted: boolean;

  /** 用户注册IP */
  @Prop({ required: true })
  registerIP: string;

  /** 用户最后登录IP */
  @Prop({ default: '' })
  lastLoginIP: string;

  /** 用户注册时间 */
  @Prop({ default: Date.now })
  createTime: Date;

  /** 用户信息上次更新时间 */
  @Prop({ default: Date.now })
  updateTime: Date;

  /** 用户最后登录时间 */
  @Prop({ default: Date.now })
  lastLoginTime: Date;

  /** 用户账户解封时间 */
  @Prop({ default: Date.now })
  lockUntil: Date;
}

// 使用SchemaFactory创建User类的Mongoose Schema实例
export const UsersSchema = SchemaFactory.createForClass(Users);
