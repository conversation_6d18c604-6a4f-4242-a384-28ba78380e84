/**
 * WebSocket 网关 - 处理实时通信的核心组件
 *
 * 功能概述：
 * 1. 用户连接管理：处理用户的连接和断开
 * 2. 消息传递：支持私聊、群聊、系统通知
 * 3. 在线状态管理：实时跟踪用户在线状态
 * 4. 房间管理：支持群组聊天室功能
 * 5. Redis集成：使用Redis进行状态持久化和消息分发
 *
 * 连接地址：ws://localhost:3000/ws
 * 认证方式：JWT Token (在连接时通过auth参数传递)
 */

// 导入NestJS WebSocket相关装饰器和接口
import {
  WebSocketGateway, // WebSocket网关装饰器
  WebSocketServer, // WebSocket服务器实例装饰器
  OnGatewayConnection, // 连接事件接口
  OnGatewayDisconnect, // 断开事件接口
  SubscribeMessage, // 消息订阅装饰器
  MessageBody, // 消息体装饰器
  ConnectedSocket, // 连接的Socket装饰器
} from '@nestjs/websockets';

// 导入Socket.IO相关类型
import { Server, Socket } from 'socket.io';
import { RedisClientType } from 'redis';

// 导入数据传输对象(DTO)定义
import { SystemNoticeDto, PrivateMessageDto, GroupMessageDto } from './dtos';

// 导入服务和模型
import { RedisService } from 'src/redis/redis.service';
import { Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Users } from 'src/users/schemas/users.schema';

/**
 * WebSocket网关配置
 * - namespace: '/ws' - WebSocket命名空间，客户端连接时需要指定
 * - cors: { origin: '*' } - 跨域配置，允许所有来源（生产环境建议限制）
 */
@WebSocketGateway({
  namespace: '/ws',
  cors: { origin: '*' },
})
export class WebsocketGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  // Socket.IO服务器实例，用于向客户端发送消息
  @WebSocketServer()
  server: Server;

  // Redis发布订阅客户端，用于跨服务器消息传递
  private redisClient: RedisClientType;

  // 日志记录器
  private readonly logger = new Logger(WebsocketGateway.name);

  /**
   * 在线用户映射表 (内存缓存)
   * 用于快速查找用户对应的Socket连接
   * Key: 用户ID (string)
   * Value: Socket连接ID (string)
   */
  private onlineUsers = new Map<string, string>();

  /**
   * 构造函数 - 初始化WebSocket网关
   * @param redisService Redis服务，用于状态持久化和消息分发
   * @param userModel 用户数据模型，用于查询用户信息
   */
  constructor(
    private readonly redisService: RedisService,
    @InjectModel(Users.name) private readonly userModel: Model<Users>
  ) {
    // 获取Redis发布订阅客户端
    this.redisClient = this.redisService.getPubSubClient();

    // 初始化Redis消息监听器
    this.initRedisListeners();
  }

  // ==================== 核心方法 ====================

  /**
   * 处理客户端连接事件
   *
   * 当客户端连接到WebSocket服务器时自动调用此方法
   * 主要功能：
   * 1. 验证用户身份（JWT Token）
   * 2. 存储用户在线状态到内存和Redis
   * 3. 记录用户设备信息和最后活跃时间
   * 4. 广播用户上线事件给所有连接的客户端
   *
   * @param client Socket连接实例，包含客户端信息
   */
  async handleConnection(@ConnectedSocket() client: Socket) {
    // 从Socket连接中提取用户ID（通过JWT Token验证）
    const userId = this.getUserIdFromSocket(client);

    // 如果用户ID无效（未认证或Token过期），断开连接
    if (!userId) {
      this.logger.warn(`未认证的连接尝试，Socket ID: ${client.id}`);
      client.disconnect(true);
      return;
    }

    try {
      // ========== 第一步：存储在线状态到内存和Redis ==========

      // 1. 内存存储：用于快速查找用户对应的Socket连接
      this.onlineUsers.set(userId, client.id);

      // 2. Redis哈希表存储：用于跨服务器实例共享在线用户信息
      await this.redisClient.hSet('online_users', userId, client.id);

      // ========== 第二步：设置用户详细状态信息 ==========

      // 获取Redis数据客户端（用于设置带过期时间的键值）
      const dataClient = this.redisService.getDataClient();

      // 设置用户在线标记（1小时后自动过期，防止僵尸状态）
      await dataClient.set(`user:${userId}:online`, 'true', { EX: 3600 });

      // 存储Socket ID（用于向特定用户发送消息）
      await dataClient.set(`user:${userId}:socket`, client.id, { EX: 3600 });

      // 记录最后活跃时间（24小时后过期）
      await dataClient.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        { EX: 86400 }
      );

      // 设置用户状态为在线
      await dataClient.set(`user:${userId}:status`, 'online', { EX: 3600 });

      // ========== 第三步：记录设备信息 ==========

      // 从HTTP头部获取用户设备信息（User-Agent）
      const device = client.handshake.headers['user-agent'] || 'unknown';
      await dataClient.set(`user:${userId}:device`, device, { EX: 3600 });

      // ========== 第四步：广播用户上线事件 ==========

      // 从数据库获取用户基本信息
      const user = await this.userModel
        .findById(userId)
        .select('_id nickname avatar') // 只选择需要的字段
        .lean(); // 使用lean()提高查询性能

      if (user) {
        // 向所有连接的客户端广播用户上线事件
        this.server.emit('user_online', {
          user: {
            id: user._id.toString(),
            nickname: user.nickname,
            avatar: user.avatar,
            status: 'online',
            lastSeen: new Date(),
          },
          timestamp: Date.now(), // 事件时间戳
        });
      }

      // 记录成功连接日志
      this.logger.log(`用户 ${userId} 已连接 (Socket: ${client.id})`);
    } catch (error) {
      // 连接处理失败时的错误处理
      this.logger.error(`处理用户连接失败: ${(error as Error).message}`);

      // 断开连接以避免不一致状态
      client.disconnect(true);
    }
  }

  /**
   * 处理客户端断开连接事件
   *
   * 当客户端断开WebSocket连接时自动调用此方法
   * 主要功能：
   * 1. 清理用户在线状态（内存和Redis）
   * 2. 更新用户最后活跃时间
   * 3. 广播用户下线事件给所有连接的客户端
   *
   * @param client Socket连接实例
   */
  async handleDisconnect(@ConnectedSocket() client: Socket) {
    // 从Socket连接中提取用户ID
    const userId = this.getUserIdFromSocket(client);

    // 如果无法获取用户ID，直接返回（可能是未认证的连接）
    if (!userId) return;

    try {
      // ========== 第一步：清理内存和Redis中的在线状态 ==========

      // 1. 从内存映射表中删除用户记录
      this.onlineUsers.delete(userId);

      // 2. 从Redis哈希表中删除用户记录
      await this.redisClient.hDel('online_users', userId);

      // ========== 第二步：清理用户详细状态信息 ==========

      const dataClient = this.redisService.getDataClient();

      // 删除在线标记
      await dataClient.del(`user:${userId}:online`);

      // 删除Socket ID记录
      await dataClient.del(`user:${userId}:socket`);

      // 更新最后活跃时间为当前时间
      await dataClient.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        { EX: 86400 } // 24小时后过期
      );

      // 设置用户状态为离线
      await dataClient.set(`user:${userId}:status`, 'offline', { EX: 3600 });

      // ========== 第三步：广播用户下线事件 ==========

      // 向所有连接的客户端广播用户下线事件
      this.server.emit('user_offline', {
        userId, // 下线用户的ID
        lastSeen: new Date(), // 最后活跃时间
        timestamp: Date.now(), // 事件时间戳
      });

      // 记录断开连接日志
      this.logger.log(`用户 ${userId} 已断开 (Socket: ${client.id})`);
    } catch (error) {
      // 断开处理失败时的错误处理
      this.logger.error(`处理用户断开失败: ${(error as Error).message}`);
    }
  }

  // ==================== 消息处理 ====================

  /**
   * 发送系统通知（管理员调用）
   */
  @SubscribeMessage('system_notice')
  async sendSystemNotice(
    @MessageBody() data: SystemNoticeDto,
    @ConnectedSocket() client: Socket
  ) {
    // 权限验证（示例）
    if (!this.isAdmin(client)) {
      return { status: 'error', message: '权限不足' };
    }

    // 广播系统通知
    this.server.emit('system_notice', {
      type: data.type,
      content: data.content,
      timestamp: Date.now(),
    });

    // 存储到 Redis
    await this.redisClient.lPush('system_notices', JSON.stringify(data));
  }

  /**
   * 发送私聊消息
   */
  @SubscribeMessage('private_message')
  async sendPrivateMessage(
    @MessageBody() data: PrivateMessageDto,
    @ConnectedSocket() client: Socket
  ) {
    const senderId = this.getUserIdFromSocket(client);
    if (!senderId) return;

    // 获取接收方 socketId
    const receiverSocketId = await this.redisClient.hGet(
      'online_users',
      data.receiverId
    );

    if (receiverSocketId) {
      // 实时推送
      this.server.to(receiverSocketId).emit('private_message', {
        from: senderId,
        content: data.content,
        timestamp: Date.now(),
      });
    }

    // 存储到 Redis
    const chatKey = `chat:${[senderId, data.receiverId].sort().join(':')}`;
    await this.redisClient.lPush(chatKey, JSON.stringify(data));
  }

  /**
   * 发送群聊消息
   */
  @SubscribeMessage('group_message')
  async sendGroupMessage(
    @MessageBody() data: GroupMessageDto,
    @ConnectedSocket() client: Socket
  ) {
    const senderId = this.getUserIdFromSocket(client);
    if (!senderId) return;

    // 广播到群组
    this.server.to(data.groupId).emit('group_message', {
      groupId: data.groupId,
      from: senderId,
      content: data.content,
      timestamp: Date.now(),
    });

    // 存储到 Redis
    await this.redisClient.lPush(
      `group:${data.groupId}:messages`,
      JSON.stringify(data)
    );
  }

  // ==================== 房间管理 ====================

  /**
   * 加入群组房间
   */
  @SubscribeMessage('join_group')
  joinGroup(@MessageBody() groupId: string, @ConnectedSocket() client: Socket) {
    client.join(groupId);
    client.emit('group_status', {
      groupId,
      action: 'joined',
      members: this.getGroupMembers(groupId),
    });
  }

  /**
   * 离开群组房间
   */
  @SubscribeMessage('leave_group')
  leaveGroup(
    @MessageBody() groupId: string,
    @ConnectedSocket() client: Socket
  ) {
    client.leave(groupId);
    client.emit('group_status', {
      groupId,
      action: 'left',
      members: this.getGroupMembers(groupId),
    });
  }

  // ==================== 用户状态管理 ====================

  /**
   * 更新用户状态
   */
  @SubscribeMessage('update_user_status')
  async updateUserStatus(
    @MessageBody() data: { status: 'online' | 'away' | 'busy' | 'offline' },
    @ConnectedSocket() client: Socket
  ) {
    const userId = this.getUserIdFromSocket(client);
    if (!userId) return;

    try {
      const dataClient = this.redisService.getDataClient();
      await dataClient.set(`user:${userId}:status`, data.status, { EX: 3600 });
      await dataClient.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        { EX: 86400 }
      );

      // 获取用户信息
      const user = await this.userModel
        .findById(userId)
        .select('_id nickname avatar')
        .lean();

      if (user) {
        // 广播状态更新
        this.server.emit('user_status_update', {
          user: {
            id: user._id.toString(),
            nickname: user.nickname,
            avatar: user.avatar,
            status: data.status,
            lastSeen: new Date(),
          },
          timestamp: Date.now(),
        });
      }

      client.emit('status_update_response', {
        success: true,
        status: data.status,
        timestamp: Date.now(),
      });

      this.logger.log(`用户 ${userId} 状态更新为 ${data.status}`);
    } catch (error) {
      this.logger.error(`更新用户状态失败: ${(error as Error).message}`);
      client.emit('status_update_response', {
        success: false,
        error: '状态更新失败',
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 获取用户在线状态
   */
  @SubscribeMessage('get_user_status')
  async getUserStatus(
    @MessageBody() data: { userId: string },
    @ConnectedSocket() client: Socket
  ) {
    try {
      const dataClient = this.redisService.getDataClient();
      const isOnline = await dataClient.get(`user:${data.userId}:online`);
      const status =
        (await dataClient.get(`user:${data.userId}:status`)) || 'offline';
      const lastSeenStr = await dataClient.get(`user:${data.userId}:last_seen`);
      const device = await dataClient.get(`user:${data.userId}:device`);

      client.emit('user_status_response', {
        success: true,
        data: {
          userId: data.userId,
          status: isOnline ? status : 'offline',
          lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
          device: device || undefined,
          isOnline: !!isOnline,
        },
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`获取用户状态失败: ${(error as Error).message}`);
      client.emit('user_status_response', {
        success: false,
        error: '获取用户状态失败',
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 批量获取用户在线状态
   */
  @SubscribeMessage('get_batch_user_status')
  async getBatchUserStatus(
    @MessageBody() data: { userIds: string[] },
    @ConnectedSocket() client: Socket
  ) {
    try {
      const dataClient = this.redisService.getDataClient();
      const userStatuses: any[] = [];

      for (const userId of data.userIds) {
        const isOnline = await dataClient.get(`user:${userId}:online`);
        const status =
          (await dataClient.get(`user:${userId}:status`)) || 'offline';
        const lastSeenStr = await dataClient.get(`user:${userId}:last_seen`);

        userStatuses.push({
          userId,
          status: isOnline ? status : 'offline',
          lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
          isOnline: !!isOnline,
        });
      }

      client.emit('batch_user_status_response', {
        success: true,
        data: userStatuses,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`批量获取用户状态失败: ${(error as Error).message}`);
      client.emit('batch_user_status_response', {
        success: false,
        error: '批量获取用户状态失败',
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 获取所有在线用户
   */
  @SubscribeMessage('get_all_online_users')
  async getAllOnlineUsers(@ConnectedSocket() client: Socket) {
    try {
      const dataClient = this.redisService.getDataClient();
      const onlineKeys = await dataClient.keys('user:*:online');
      const userIds = onlineKeys.map((key) => key.split(':')[1]);

      if (userIds.length === 0) {
        client.emit('all_online_users_response', {
          success: true,
          data: { count: 0, users: [] },
          timestamp: Date.now(),
        });
        return;
      }

      // 获取用户详细信息
      const users = await this.userModel
        .find({ _id: { $in: userIds } })
        .select('_id nickname avatar')
        .lean();

      const onlineUsers: any[] = [];
      for (const user of users) {
        const userId = user._id.toString();
        const status =
          (await dataClient.get(`user:${userId}:status`)) || 'online';
        const lastSeenStr = await dataClient.get(`user:${userId}:last_seen`);

        onlineUsers.push({
          id: userId,
          nickname: user.nickname,
          avatar: user.avatar,
          status,
          lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
        });
      }

      client.emit('all_online_users_response', {
        success: true,
        data: {
          count: onlineUsers.length,
          users: onlineUsers,
        },
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`获取所有在线用户失败: ${(error as Error).message}`);
      client.emit('all_online_users_response', {
        success: false,
        error: '获取在线用户失败',
        timestamp: Date.now(),
      });
    }
  }

  // ==================== 辅助方法 ====================

  private initRedisListeners() {
    // 系统级广播通道
    this.redisClient.subscribe('global_notices', (err) => {
      if (err) {
        console.error('订阅系统广播通道失败', err);
      } else {
        console.log('订阅系统广播通道成功');
      }
    });

    this.redisClient.on('message', (channel, message: string) => {
      switch (channel) {
        case 'global_notices':
          this.handleGlobalNotice(message);
          break;
      }
    });
  }

  private handleGlobalNotice(message: string) {
    this.server.emit('global_notice', JSON.parse(message));
  }

  private getUserIdFromSocket(client: Socket): string | null {
    // 从认证信息中获取用户ID（需结合你的认证系统）
    return client.handshake.auth.userId || null;
  }

  private isAdmin(client: Socket): boolean {
    // 实现你的管理员验证逻辑
    return client.handshake.auth.role === 'admin';
  }

  private getGroupMembers(groupId: string): string[] {
    // 获取房间成员列表
    const room = this.server.sockets.adapter.rooms.get(groupId);
    return room ? Array.from(room) : [];
  }
}
