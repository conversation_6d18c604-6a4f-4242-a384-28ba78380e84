// src/websocket/gateway/websocket.gateway.ts
import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { RedisClientType } from 'redis';
import { SystemNoticeDto, PrivateMessageDto, GroupMessageDto } from './dtos';
import { RedisService } from 'src/redis/redis.service';
import { Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Users } from 'src/users/schemas/users.schema';

@WebSocketGateway({
  namespace: '/ws',
  cors: { origin: '*' },
})
export class WebsocketGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;
  private redisClient: RedisClientType;
  private readonly logger = new Logger(WebsocketGateway.name);

  // 在线用户映射表 (内存缓存)
  private onlineUsers = new Map<string, string>(); // userId -> socketId

  constructor(
    private readonly redisService: RedisService,
    @InjectModel(Users.name) private readonly userModel: Model<Users>
  ) {
    this.redisClient = this.redisService.getPubSubClient();
    this.initRedisListeners();
  }

  // ==================== 核心方法 ====================

  /**
   * 处理客户端连接
   */
  async handleConnection(@ConnectedSocket() client: Socket) {
    const userId = this.getUserIdFromSocket(client);
    if (!userId) {
      client.disconnect(true);
      return;
    }

    try {
      // 存储在线状态到内存和Redis
      this.onlineUsers.set(userId, client.id);
      await this.redisClient.hSet('online_users', userId, client.id);

      // 设置用户在线状态和最后活跃时间
      const dataClient = this.redisService.getDataClient();
      await dataClient.set(`user:${userId}:online`, 'true', { EX: 3600 }); // 1小时过期
      await dataClient.set(`user:${userId}:socket`, client.id, { EX: 3600 });
      await dataClient.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        { EX: 86400 }
      ); // 24小时过期
      await dataClient.set(`user:${userId}:status`, 'online', { EX: 3600 });

      // 获取设备信息
      const device = client.handshake.headers['user-agent'] || 'unknown';
      await dataClient.set(`user:${userId}:device`, device, { EX: 3600 });

      // 获取用户信息并广播上线事件
      const user = await this.userModel
        .findById(userId)
        .select('_id nickname avatar')
        .lean();

      if (user) {
        // 广播用户上线事件
        this.server.emit('user_online', {
          user: {
            id: user._id.toString(),
            nickname: user.nickname,
            avatar: user.avatar,
            status: 'online',
            lastSeen: new Date(),
          },
          timestamp: Date.now(),
        });
      }

      this.logger.log(`用户 ${userId} 已连接 (Socket: ${client.id})`);
    } catch (error) {
      this.logger.error(`处理用户连接失败: ${(error as Error).message}`);
      client.disconnect(true);
    }
  }

  /**
   * 处理客户端断开
   */
  async handleDisconnect(@ConnectedSocket() client: Socket) {
    const userId = this.getUserIdFromSocket(client);
    if (!userId) return;

    try {
      // 清理内存和Redis中的在线状态
      this.onlineUsers.delete(userId);
      await this.redisClient.hDel('online_users', userId);

      // 清理用户在线状态
      const dataClient = this.redisService.getDataClient();
      await dataClient.del(`user:${userId}:online`);
      await dataClient.del(`user:${userId}:socket`);
      await dataClient.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        { EX: 86400 }
      );
      await dataClient.set(`user:${userId}:status`, 'offline', { EX: 3600 });

      // 广播用户下线事件
      this.server.emit('user_offline', {
        userId,
        lastSeen: new Date(),
        timestamp: Date.now(),
      });

      this.logger.log(`用户 ${userId} 已断开 (Socket: ${client.id})`);
    } catch (error) {
      this.logger.error(`处理用户断开失败: ${(error as Error).message}`);
    }
  }

  // ==================== 消息处理 ====================

  /**
   * 发送系统通知（管理员调用）
   */
  @SubscribeMessage('system_notice')
  async sendSystemNotice(
    @MessageBody() data: SystemNoticeDto,
    @ConnectedSocket() client: Socket
  ) {
    // 权限验证（示例）
    if (!this.isAdmin(client)) {
      return { status: 'error', message: '权限不足' };
    }

    // 广播系统通知
    this.server.emit('system_notice', {
      type: data.type,
      content: data.content,
      timestamp: Date.now(),
    });

    // 存储到 Redis
    await this.redisClient.lPush('system_notices', JSON.stringify(data));
  }

  /**
   * 发送私聊消息
   */
  @SubscribeMessage('private_message')
  async sendPrivateMessage(
    @MessageBody() data: PrivateMessageDto,
    @ConnectedSocket() client: Socket
  ) {
    const senderId = this.getUserIdFromSocket(client);
    if (!senderId) return;

    // 获取接收方 socketId
    const receiverSocketId = await this.redisClient.hGet(
      'online_users',
      data.receiverId
    );

    if (receiverSocketId) {
      // 实时推送
      this.server.to(receiverSocketId).emit('private_message', {
        from: senderId,
        content: data.content,
        timestamp: Date.now(),
      });
    }

    // 存储到 Redis
    const chatKey = `chat:${[senderId, data.receiverId].sort().join(':')}`;
    await this.redisClient.lPush(chatKey, JSON.stringify(data));
  }

  /**
   * 发送群聊消息
   */
  @SubscribeMessage('group_message')
  async sendGroupMessage(
    @MessageBody() data: GroupMessageDto,
    @ConnectedSocket() client: Socket
  ) {
    const senderId = this.getUserIdFromSocket(client);
    if (!senderId) return;

    // 广播到群组
    this.server.to(data.groupId).emit('group_message', {
      groupId: data.groupId,
      from: senderId,
      content: data.content,
      timestamp: Date.now(),
    });

    // 存储到 Redis
    await this.redisClient.lPush(
      `group:${data.groupId}:messages`,
      JSON.stringify(data)
    );
  }

  // ==================== 房间管理 ====================

  /**
   * 加入群组房间
   */
  @SubscribeMessage('join_group')
  joinGroup(@MessageBody() groupId: string, @ConnectedSocket() client: Socket) {
    client.join(groupId);
    client.emit('group_status', {
      groupId,
      action: 'joined',
      members: this.getGroupMembers(groupId),
    });
  }

  /**
   * 离开群组房间
   */
  @SubscribeMessage('leave_group')
  leaveGroup(
    @MessageBody() groupId: string,
    @ConnectedSocket() client: Socket
  ) {
    client.leave(groupId);
    client.emit('group_status', {
      groupId,
      action: 'left',
      members: this.getGroupMembers(groupId),
    });
  }

  // ==================== 用户状态管理 ====================

  /**
   * 更新用户状态
   */
  @SubscribeMessage('update_user_status')
  async updateUserStatus(
    @MessageBody() data: { status: 'online' | 'away' | 'busy' | 'offline' },
    @ConnectedSocket() client: Socket
  ) {
    const userId = this.getUserIdFromSocket(client);
    if (!userId) return;

    try {
      const dataClient = this.redisService.getDataClient();
      await dataClient.set(`user:${userId}:status`, data.status, { EX: 3600 });
      await dataClient.set(
        `user:${userId}:last_seen`,
        new Date().toISOString(),
        { EX: 86400 }
      );

      // 获取用户信息
      const user = await this.userModel
        .findById(userId)
        .select('_id nickname avatar')
        .lean();

      if (user) {
        // 广播状态更新
        this.server.emit('user_status_update', {
          user: {
            id: user._id.toString(),
            nickname: user.nickname,
            avatar: user.avatar,
            status: data.status,
            lastSeen: new Date(),
          },
          timestamp: Date.now(),
        });
      }

      client.emit('status_update_response', {
        success: true,
        status: data.status,
        timestamp: Date.now(),
      });

      this.logger.log(`用户 ${userId} 状态更新为 ${data.status}`);
    } catch (error) {
      this.logger.error(`更新用户状态失败: ${(error as Error).message}`);
      client.emit('status_update_response', {
        success: false,
        error: '状态更新失败',
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 获取用户在线状态
   */
  @SubscribeMessage('get_user_status')
  async getUserStatus(
    @MessageBody() data: { userId: string },
    @ConnectedSocket() client: Socket
  ) {
    try {
      const dataClient = this.redisService.getDataClient();
      const isOnline = await dataClient.get(`user:${data.userId}:online`);
      const status =
        (await dataClient.get(`user:${data.userId}:status`)) || 'offline';
      const lastSeenStr = await dataClient.get(`user:${data.userId}:last_seen`);
      const device = await dataClient.get(`user:${data.userId}:device`);

      client.emit('user_status_response', {
        success: true,
        data: {
          userId: data.userId,
          status: isOnline ? status : 'offline',
          lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
          device: device || undefined,
          isOnline: !!isOnline,
        },
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`获取用户状态失败: ${(error as Error).message}`);
      client.emit('user_status_response', {
        success: false,
        error: '获取用户状态失败',
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 批量获取用户在线状态
   */
  @SubscribeMessage('get_batch_user_status')
  async getBatchUserStatus(
    @MessageBody() data: { userIds: string[] },
    @ConnectedSocket() client: Socket
  ) {
    try {
      const dataClient = this.redisService.getDataClient();
      const userStatuses: any[] = [];

      for (const userId of data.userIds) {
        const isOnline = await dataClient.get(`user:${userId}:online`);
        const status =
          (await dataClient.get(`user:${userId}:status`)) || 'offline';
        const lastSeenStr = await dataClient.get(`user:${userId}:last_seen`);

        userStatuses.push({
          userId,
          status: isOnline ? status : 'offline',
          lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
          isOnline: !!isOnline,
        });
      }

      client.emit('batch_user_status_response', {
        success: true,
        data: userStatuses,
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`批量获取用户状态失败: ${(error as Error).message}`);
      client.emit('batch_user_status_response', {
        success: false,
        error: '批量获取用户状态失败',
        timestamp: Date.now(),
      });
    }
  }

  /**
   * 获取所有在线用户
   */
  @SubscribeMessage('get_all_online_users')
  async getAllOnlineUsers(@ConnectedSocket() client: Socket) {
    try {
      const dataClient = this.redisService.getDataClient();
      const onlineKeys = await dataClient.keys('user:*:online');
      const userIds = onlineKeys.map((key) => key.split(':')[1]);

      if (userIds.length === 0) {
        client.emit('all_online_users_response', {
          success: true,
          data: { count: 0, users: [] },
          timestamp: Date.now(),
        });
        return;
      }

      // 获取用户详细信息
      const users = await this.userModel
        .find({ _id: { $in: userIds } })
        .select('_id nickname avatar')
        .lean();

      const onlineUsers: any[] = [];
      for (const user of users) {
        const userId = user._id.toString();
        const status =
          (await dataClient.get(`user:${userId}:status`)) || 'online';
        const lastSeenStr = await dataClient.get(`user:${userId}:last_seen`);

        onlineUsers.push({
          id: userId,
          nickname: user.nickname,
          avatar: user.avatar,
          status,
          lastSeen: lastSeenStr ? new Date(lastSeenStr) : new Date(),
        });
      }

      client.emit('all_online_users_response', {
        success: true,
        data: {
          count: onlineUsers.length,
          users: onlineUsers,
        },
        timestamp: Date.now(),
      });
    } catch (error) {
      this.logger.error(`获取所有在线用户失败: ${(error as Error).message}`);
      client.emit('all_online_users_response', {
        success: false,
        error: '获取在线用户失败',
        timestamp: Date.now(),
      });
    }
  }

  // ==================== 辅助方法 ====================

  private initRedisListeners() {
    // 系统级广播通道
    this.redisClient.subscribe('global_notices', (err) => {
      if (err) {
        console.error('订阅系统广播通道失败', err);
      } else {
        console.log('订阅系统广播通道成功');
      }
    });

    this.redisClient.on('message', (channel, message: string) => {
      switch (channel) {
        case 'global_notices':
          this.handleGlobalNotice(message);
          break;
      }
    });
  }

  private handleGlobalNotice(message: string) {
    this.server.emit('global_notice', JSON.parse(message));
  }

  private getUserIdFromSocket(client: Socket): string | null {
    // 从认证信息中获取用户ID（需结合你的认证系统）
    return client.handshake.auth.userId || null;
  }

  private isAdmin(client: Socket): boolean {
    // 实现你的管理员验证逻辑
    return client.handshake.auth.role === 'admin';
  }

  private getGroupMembers(groupId: string): string[] {
    // 获取房间成员列表
    const room = this.server.sockets.adapter.rooms.get(groupId);
    return room ? Array.from(room) : [];
  }
}
