// src/websocket/gateway/websocket.gateway.ts
import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { RedisClientType } from 'redis';
import { SystemNoticeDto, PrivateMessageDto, GroupMessageDto } from './dtos';
import { RedisService } from 'src/redis/redis.service';

@WebSocketGateway({
  namespace: '/ws',
  cors: { origin: '*' },
})
export class WebsocketGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;
  private redisClient: RedisClientType;

  // 在线用户映射表 (内存缓存)
  private onlineUsers = new Map<string, string>(); // userId -> socketId

  constructor(private readonly redisService: RedisService) {
    this.redisClient = this.redisService.getPubSubClient();
    this.initRedisListeners();
  }

  // ==================== 核心方法 ====================

  /**
   * 处理客户端连接
   */
  async handleConnection(@ConnectedSocket() client: Socket) {
    const userId = this.getUserIdFromSocket(client);
    if (!userId) {
      client.disconnect(true);
      return;
    }

    // 存储在线状态
    this.onlineUsers.set(userId, client.id);
    await this.redisClient.hSet('online_users', userId, client.id);

    console.log(`用户 ${userId} 已连接`);
  }

  /**
   * 处理客户端断开
   */
  async handleDisconnect(@ConnectedSocket() client: Socket) {
    const userId = this.getUserIdFromSocket(client);
    if (!userId) return;

    // 清理在线状态
    this.onlineUsers.delete(userId);
    await this.redisClient.hDel('online_users', userId);

    console.log(`用户 ${userId} 已断开`);
  }

  // ==================== 消息处理 ====================

  /**
   * 发送系统通知（管理员调用）
   */
  @SubscribeMessage('system_notice')
  async sendSystemNotice(
    @MessageBody() data: SystemNoticeDto,
    @ConnectedSocket() client: Socket
  ) {
    // 权限验证（示例）
    if (!this.isAdmin(client)) {
      return { status: 'error', message: '权限不足' };
    }

    // 广播系统通知
    this.server.emit('system_notice', {
      type: data.type,
      content: data.content,
      timestamp: Date.now(),
    });

    // 存储到 Redis
    await this.redisClient.lPush('system_notices', JSON.stringify(data));
  }

  /**
   * 发送私聊消息
   */
  @SubscribeMessage('private_message')
  async sendPrivateMessage(
    @MessageBody() data: PrivateMessageDto,
    @ConnectedSocket() client: Socket
  ) {
    const senderId = this.getUserIdFromSocket(client);
    if (!senderId) return;

    // 获取接收方 socketId
    const receiverSocketId = await this.redisClient.hGet(
      'online_users',
      data.receiverId
    );

    if (receiverSocketId) {
      // 实时推送
      this.server.to(receiverSocketId).emit('private_message', {
        from: senderId,
        content: data.content,
        timestamp: Date.now(),
      });
    }

    // 存储到 Redis
    const chatKey = `chat:${[senderId, data.receiverId].sort().join(':')}`;
    await this.redisClient.lPush(chatKey, JSON.stringify(data));
  }

  /**
   * 发送群聊消息
   */
  @SubscribeMessage('group_message')
  async sendGroupMessage(
    @MessageBody() data: GroupMessageDto,
    @ConnectedSocket() client: Socket
  ) {
    const senderId = this.getUserIdFromSocket(client);
    if (!senderId) return;

    // 广播到群组
    this.server.to(data.groupId).emit('group_message', {
      groupId: data.groupId,
      from: senderId,
      content: data.content,
      timestamp: Date.now(),
    });

    // 存储到 Redis
    await this.redisClient.lPush(
      `group:${data.groupId}:messages`,
      JSON.stringify(data)
    );
  }

  // ==================== 房间管理 ====================

  /**
   * 加入群组房间
   */
  @SubscribeMessage('join_group')
  joinGroup(@MessageBody() groupId: string, @ConnectedSocket() client: Socket) {
    client.join(groupId);
    client.emit('group_status', {
      groupId,
      action: 'joined',
      members: this.getGroupMembers(groupId),
    });
  }

  /**
   * 离开群组房间
   */
  @SubscribeMessage('leave_group')
  leaveGroup(
    @MessageBody() groupId: string,
    @ConnectedSocket() client: Socket
  ) {
    client.leave(groupId);
    client.emit('group_status', {
      groupId,
      action: 'left',
      members: this.getGroupMembers(groupId),
    });
  }

  // ==================== 辅助方法 ====================

  private initRedisListeners() {
    // 系统级广播通道
    this.redisClient.subscribe('global_notices', (err) => {
      if (err) {
        console.error('订阅系统广播通道失败', err);
      } else {
        console.log('订阅系统广播通道成功');
      }
    });

    this.redisClient.on('message', (channel, message: string) => {
      switch (channel) {
        case 'global_notices':
          this.handleGlobalNotice(message);
          break;
      }
    });
  }

  private handleGlobalNotice(message: string) {
    this.server.emit('global_notice', JSON.parse(message));
  }

  private getUserIdFromSocket(client: Socket): string | null {
    // 从认证信息中获取用户ID（需结合你的认证系统）
    return client.handshake.auth.userId || null;
  }

  private isAdmin(client: Socket): boolean {
    // 实现你的管理员验证逻辑
    return client.handshake.auth.role === 'admin';
  }

  private getGroupMembers(groupId: string): string[] {
    // 获取房间成员列表
    const room = this.server.sockets.adapter.rooms.get(groupId);
    return room ? Array.from(room) : [];
  }
}
