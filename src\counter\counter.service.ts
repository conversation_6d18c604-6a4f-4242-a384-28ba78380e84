import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Counter } from './schemas/counter.schema';

@Injectable()
export class CounterService {
  constructor(
    @InjectModel(Counter.name)
    private readonly counterModel: Model<Counter>
  ) {}

  /**
   * ?: 获取下一个序列号
   * @param name 序列名称
   * @returns 下一个序列号
   */
  async getNextSequence(name: string): Promise<number> {
    const result = await this.counterModel.findOneAndUpdate(
      { name },
      { $inc: { seq: 1 } },
      { new: true, upsert: true }
    );
    return result.seq;
  }

  /**
   * ?: 格式化序列号
   * @param name 序列名称
   * @param length 序列长度
   * @returns 格式化后的序列号
   */
  async formatSequence(name: string, length = 6): Promise<string> {
    const seq = await this.getNextSequence(name);
    return seq.toString().padStart(length, '0');
  }
}
