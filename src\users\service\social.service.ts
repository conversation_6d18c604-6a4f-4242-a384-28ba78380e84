import { HttpException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Users } from '../schemas/users.schema';
import { Model } from 'mongoose';
import { UsersFollow } from '../schemas/users-follow.schema';
import { Contents } from 'src/contents/schemas/contents.schema';
import { ContentsLike } from 'src/contents/schemas/contents-like.schema';
import { ContentsFavorite } from 'src/contents/schemas/contents-favorite.schema';
import { Photos } from 'src/photos/schemas/photos.schema';
import { PhotosLike } from 'src/photos/schemas/photos-like.schema';
import { PhotosFavorite } from 'src/photos/schemas/photos-favorite.schema';
import { UsersService } from './users.service';
import { HttpStatusEnum } from 'src/shared/enums/HttpStatusCode.enum';

/**
 * 用户社交服务：负责处理用户之间的互动操作，包括关注、点赞、收藏等。
 * 该服务使用 Mongoose 操作数据库，并支持事务处理以确保数据一致性。
 */
@Injectable() // 标记 SocialService 类为可注入的服务
export class SocialService {
  constructor(
    @InjectModel(Users.name) private usersModel: Model<Users>,
    @InjectModel(UsersFollow.name) private usersFollowModel: Model<UsersFollow>,
    @InjectModel(Contents.name) private contentsModel: Model<Contents>,
    @InjectModel(ContentsLike.name)
    private contentsLikeModel: Model<ContentsLike>,
    @InjectModel(ContentsFavorite.name)
    private contentsFavoriteModel: Model<ContentsFavorite>,
    @InjectModel(Photos.name) private photosModel: Model<Photos>,
    @InjectModel(PhotosLike.name) private photosLikeModel: Model<PhotosLike>,
    @InjectModel(PhotosFavorite.name)
    private photosFavoriteModel: Model<PhotosFavorite>,
    private readonly userService: UsersService
  ) {}

  /**
   * !: 关注或取消关注用户
   * @param followerId 关注者的用户 ID
   * @param followingUID 被关注者的用户 UID
   * @description
   * 1. 如果关注关系不存在，则执行关注操作。
   * 2. 如果关注关系已存在，则执行取消关注操作。
   * 3. 使用事务确保操作的原子性。
   * @returns 返回一个布尔值，表示是否成功执行了关注或取消关注操作。
   */
  async toggleFollowUser(
    followerId: string,
    followingUID: string
  ): Promise<boolean> {
    const following = await this.userService.findOneByUid(followingUID);

    // 检查是否为本人
    if (followerId === following?.id) {
      throw new HttpException('不能关注自己', HttpStatusEnum.BAD_REQUEST);
    }

    // 检查是否已关注
    const exists = await this.usersFollowModel.exists({
      follower: followerId,
      following: following?.id,
    });

    if (exists) {
      // 如果已关注，则执行取消关注逻辑
      await this.unfollowUserInternal(followerId, following.id);
      return false; // 返回 false 表示取消关注
    } else {
      // 如果未关注，则执行关注逻辑
      await this.followUserInternal(followerId, following.id);
      return true; // 返回 true 表示关注
    }
  }

  /**
   * !: 关注用户的内部逻辑
   * @param followerId 关注者的用户 ID
   * @param followingId 被关注者的用户 ID
   * @description
   * 1. 创建新的关注关系。
   * 2. 增加关注者的关注数量（followingsCount）。
   * 3. 增加被关注者的粉丝数量（followersCount）。
   */
  private async followUserInternal(
    followerId: string,
    followingId: string
  ): Promise<void> {
    // 创建新的关注关系
    await this.usersFollowModel.create({
      follower: followerId,
      following: followingId,
    });

    // 更新关注者的关注数量（followingsCount）
    await this.usersModel.findByIdAndUpdate(followerId, {
      $inc: { followingsCount: 1 },
    });

    // 更新被关注者的粉丝数量（followersCount）
    await this.usersModel.findByIdAndUpdate(followingId, {
      $inc: { followersCount: 1 },
    });
  }

  /**
   * !: 取消关注用户的内部逻辑
   * @param followerId 关注者的用户 ID
   * @param followingId 被关注者的用户 ID
   * @description
   * 1. 删除关注关系。
   * 2. 减少关注者的关注数量（followingsCount）。
   * 3. 减少被关注者的粉丝数量（followersCount）。
   */
  private async unfollowUserInternal(
    followerId: string,
    followingId: string
  ): Promise<void> {
    // 删除关注关系
    await this.usersFollowModel.deleteOne({
      follower: followerId,
      following: followingId,
    });

    // 更新关注者的关注数量（followingsCount）
    await this.usersModel.findByIdAndUpdate(followerId, {
      $inc: { followingsCount: -1 },
    });

    // 更新被关注者的粉丝数量（followersCount）
    await this.usersModel.findByIdAndUpdate(followingId, {
      $inc: { followersCount: -1 },
    });
  }

  /**
   * !: 获取用户的关注列表（分页）
   * @param UID 被查看的 用户 UID
   * @param id 查看的用户 ID
   * @param options 分页和排序参数
   * @returns 返回一个包含用户关注列表的分页对象。
   */
  async getFollowings(
    UID: string,
    id?: string,
    options?: {
      page?: number;
      pageSize?: number;
      sortField?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ) {
    const userId = (await this.userService.findOneByUid(UID)).id;
    const settings = await this.userService.getSetting(UID);
    if (!settings?.showFollowList && id !== userId) {
      throw new HttpException('隐私设置不允许查看', HttpStatusEnum.BAD_REQUEST);
    }
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'desc',
    } = options || {};
    const skip = (page - 1) * pageSize;
    const totalCount = await this.usersFollowModel.countDocuments({
      follower: userId,
    });
    const followings = await this.usersFollowModel
      .find({ follower: userId })
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(pageSize)
      .select('following -_id');
    const followingIds = followings.map((f) => f.following);

    // 新增：查询当前用户（id）是否关注了这些用户
    let followedSet = new Set<string>();
    if (id) {
      const followed = await this.usersFollowModel
        .find({ follower: id, following: { $in: followingIds } })
        .select('following');
      followedSet = new Set(followed.map((f) => f.following.toString()));
    }

    const followingsList = await this.usersModel
      .find({ _id: { $in: followingIds } })
      .select('uid avatar nickname');
    return {
      page,
      pageSize,
      totalCount,
      totalPage: Math.ceil(totalCount / pageSize),
      sortField,
      sortOrder,
      list: followingsList.map((v) => {
        const obj = v.toJSON();
        return {
          ...obj,
          isFollowed: followedSet.has(obj._id.toString()),
        };
      }),
    };
  }

  /**
   * !: 获取用户的粉丝列表（分页）
   * @param UID 被查看的 用户 UID
   * @param id 查看的用户 ID
   * @param options 分页和排序参数
   * @returns 返回一个包含用户粉丝列表的分页对象。
   */
  async getFollowers(
    UID: string,
    id?: string,
    options?: {
      page?: number;
      pageSize?: number;
      sortField?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ) {
    const userId = (await this.userService.findOneByUid(UID)).id;
    const settings = await this.userService.getSetting(UID);
    if (!settings?.showFansList && id !== userId) {
      throw new HttpException('隐私设置不允许查看', HttpStatusEnum.BAD_REQUEST);
    }
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'desc',
    } = options || {};
    const skip = (page - 1) * pageSize;
    const totalCount = await this.usersFollowModel.countDocuments({
      following: userId,
    });
    const followers = await this.usersFollowModel
      .find({ following: userId })
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(pageSize)
      .select('follower -_id');
    const followerIds = followers.map((f) => f.follower);

    // 新增：查询当前用户（id）是否关注了这些粉丝
    let followedSet = new Set<string>();
    if (id) {
      const followed = await this.usersFollowModel
        .find({ follower: id, following: { $in: followerIds } })
        .select('following');
      followedSet = new Set(followed.map((f) => f.following.toString()));
    }

    const followersList = await this.usersModel
      .find({ _id: { $in: followerIds } })
      .select('uid avatar nickname');
    return {
      page,
      pageSize,
      totalCount,
      totalPage: Math.ceil(totalCount / pageSize),
      sortField,
      sortOrder,
      list: followersList.map((v) => {
        const obj = v.toJSON();
        return {
          ...obj,
          isFollowed: followedSet.has(obj._id.toString()),
        };
      }),
    };
  }

  /**
   * !: 获取用户的点赞图片列表（分页）
   * @param UID 被查看的 用户 UID
   * @param id 查看的用户 ID
   * @param options 分页和排序参数
   * @returns 返回一个包含用户点赞图片列表的分页对象。
   */
  async getPhotosLikes(
    UID: string,
    id?: string,
    options?: {
      page?: number;
      pageSize?: number;
      sortField?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ) {
    const userId = (await this.userService.findOneByUid(UID)).id;
    const settings = await this.userService.getSetting(UID);
    if (!settings?.showLikePhotoList && id !== userId) {
      throw new HttpException('隐私设置不允许查看', HttpStatusEnum.BAD_REQUEST);
    }
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'desc',
    } = options || {};
    const skip = (page - 1) * pageSize;
    const totalCount = await this.photosLikeModel.countDocuments({
      user: userId,
    });
    const photosLikes = await this.photosLikeModel
      .find({ user: userId })
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(pageSize)
      .select('photo -_id');
    const photoIds = photosLikes.map((f) => f.photo);

    // 新增：查询当前用户（id）对这些图片的点赞/收藏关系
    let likedSet = new Set<string>();
    let favoritedSet = new Set<string>();
    if (id) {
      const [liked, favorited] = await Promise.all([
        this.photosLikeModel
          .find({ user: id, photo: { $in: photoIds } })
          .select('photo'),
        this.photosFavoriteModel
          .find({ user: id, photo: { $in: photoIds } })
          .select('photo'),
      ]);
      likedSet = new Set(liked.map((l) => l.photo.toString()));
      favoritedSet = new Set(favorited.map((f) => f.photo.toString()));
    }

    const photosLikesList = await this.photosModel
      .find({ _id: { $in: photoIds } })
      .select(
        'id filename url tags favoriteCount likeCount isLiked isFavorited downloadCount attributes'
      );
    return {
      page,
      pageSize,
      totalCount,
      totalPage: Math.ceil(totalCount / pageSize),
      sortField,
      sortOrder,
      list: photosLikesList.map((v) => {
        const obj = v.toJSON();
        return {
          ...obj,
          isLiked: likedSet.has(obj.id),
          isFavorited: favoritedSet.has(obj.id),
        };
      }),
    };
  }

  /**
   * !: 获取用户的收藏的图片列表（分页）
   * @param UID 被查看的 用户 UID
   * @param id 查看的用户 ID
   * @param options 分页和排序参数
   * @returns 返回一个包含用户收藏图片列表的分页对象。
   */
  async getPhotosFavorites(
    UID: string,
    id?: string,
    options?: {
      page?: number;
      pageSize?: number;
      sortField?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ) {
    const userId = (await this.userService.findOneByUid(UID)).id;
    const settings = await this.userService.getSetting(UID);
    if (!settings?.showFavoritePhotoList && id !== userId) {
      throw new HttpException('隐私设置不允许查看', HttpStatusEnum.BAD_REQUEST);
    }
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'desc',
    } = options || {};
    const skip = (page - 1) * pageSize;
    const totalCount = await this.photosFavoriteModel.countDocuments({
      user: userId,
    });
    const photosFavorites = await this.photosFavoriteModel
      .find({ user: userId })
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(pageSize)
      .select('photo -_id');
    const photoIds = photosFavorites.map((f) => f.photo);

    // 新增：查询当前用户（id）对这些图片的点赞/收藏关系
    let likedSet = new Set<string>();
    let favoritedSet = new Set<string>();
    if (id) {
      const [liked, favorited] = await Promise.all([
        this.photosLikeModel
          .find({ user: id, photo: { $in: photoIds } })
          .select('photo'),
        this.photosFavoriteModel
          .find({ user: id, photo: { $in: photoIds } })
          .select('photo'),
      ]);
      likedSet = new Set(liked.map((l) => l.photo.toString()));
      favoritedSet = new Set(favorited.map((f) => f.photo.toString()));
    }

    const photosFavoritesList = await this.photosModel
      .find({ _id: { $in: photoIds } })
      .select(
        'id filename url tags favoriteCount likeCount isLiked isFavorited downloadCount attributes'
      );
    return {
      page,
      pageSize,
      totalCount,
      totalPage: Math.ceil(totalCount / pageSize),
      sortField,
      sortOrder,
      list: photosFavoritesList.map((v) => {
        const obj = v.toJSON();
        return {
          ...obj,
          isLiked: likedSet.has(obj.id),
          isFavorited: favoritedSet.has(obj.id),
        };
      }),
    };
  }

  /**
   * !: 获取用户的点赞的帖子列表（分页）
   * @param UID 被查看的 用户 UID
   * @param id 查看的用户 ID
   * @param options 分页和排序参数
   * @returns 返回一个包含用户点赞帖子列表的分页对象。
   */
  async getContentsLikes(
    UID: string,
    id?: string,
    options?: {
      page?: number;
      pageSize?: number;
      sortField?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ) {
    const userId = (await this.userService.findOneByUid(UID)).id;
    const settings = await this.userService.getSetting(UID);
    if (!settings?.showLikeContentList && id !== userId) {
      throw new HttpException('隐私设置不允许查看', HttpStatusEnum.BAD_REQUEST);
    }
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'desc',
    } = options || {};
    const skip = (page - 1) * pageSize;
    const totalCount = await this.contentsLikeModel.countDocuments({
      user: userId,
    });
    const contentsLikes = await this.contentsLikeModel
      .find({ user: userId })
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(pageSize)
      .select('content -_id');
    const contentIds = contentsLikes.map((f) => f.content);

    // 新增：查询当前用户（id）对这些帖子的点赞/收藏关系
    let likedSet = new Set<string>();
    let favoritedSet = new Set<string>();
    if (id) {
      const [liked, favorited] = await Promise.all([
        this.contentsLikeModel
          .find({ user: id, content: { $in: contentIds } })
          .select('content'),
        this.contentsFavoriteModel
          .find({ user: id, content: { $in: contentIds } })
          .select('content'),
      ]);
      likedSet = new Set(liked.map((l) => l.content.toString()));
      favoritedSet = new Set(favorited.map((f) => f.content.toString()));
    }

    const contentsLikesList = await this.contentsModel
      .find({ _id: { $in: contentIds } })
      .select(
        'id user title content photos favoriteCount likeCount isLiked isFavorited'
      )
      .populate({
        path: 'user',
        select: 'uid nickname avatar',
      })
      .populate({
        path: 'photos',
        select: 'id filename url attributes',
      });
    return {
      page,
      pageSize,
      totalCount,
      totalPage: Math.ceil(totalCount / pageSize),
      sortField,
      sortOrder,
      list: contentsLikesList.map((v) => {
        const obj = v.toJSON();
        return {
          ...obj,
          isLiked: likedSet.has(obj.id),
          isFavorited: favoritedSet.has(obj.id),
        };
      }),
    };
  }

  /**
   * !: 获取用户的收藏的帖子列表（分页）
   * @param UID 被查看的 用户 UID
   * @param id 查看的用户 ID
   * @param options 分页和排序参数
   * @returns 返回一个包含用户收藏帖子列表的分页对象。
   */
  async getContentsFavorites(
    UID: string,
    id?: string,
    options?: {
      page?: number;
      pageSize?: number;
      sortField?: string;
      sortOrder?: 'asc' | 'desc';
    }
  ) {
    const userId = (await this.userService.findOneByUid(UID)).id;
    const settings = await this.userService.getSetting(UID);
    if (!settings?.showFavoriteContentList && id !== userId) {
      throw new HttpException('隐私设置不允许查看', HttpStatusEnum.BAD_REQUEST);
    }
    const {
      page = 1,
      pageSize = 10,
      sortField = 'createTime',
      sortOrder = 'desc',
    } = options || {};
    const skip = (page - 1) * pageSize;
    const totalCount = await this.contentsFavoriteModel.countDocuments({
      user: userId,
    });
    const contentsFavorites = await this.contentsFavoriteModel
      .find({ user: userId })
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(pageSize)
      .select('content -_id');
    const contentIds = contentsFavorites.map((f) => f.content);

    // 新增：查询当前用户（id）对这些帖子的点赞/收藏关系
    let likedSet = new Set<string>();
    let favoritedSet = new Set<string>();
    if (id) {
      const [liked, favorited] = await Promise.all([
        this.contentsLikeModel
          .find({ user: id, content: { $in: contentIds } })
          .select('content'),
        this.contentsFavoriteModel
          .find({ user: id, content: { $in: contentIds } })
          .select('content'),
      ]);
      likedSet = new Set(liked.map((l) => l.content.toString()));
      favoritedSet = new Set(favorited.map((f) => f.content.toString()));
    }

    const contentsFavoritesList = await this.contentsModel
      .find({ _id: { $in: contentIds } })
      .select(
        'id user title content photos favoriteCount likeCount isLiked isFavorited'
      )
      .populate({
        path: 'user',
        select: 'uid nickname avatar',
      })
      .populate({
        path: 'photos',
        select: 'id filename url attributes',
      });
    return {
      page,
      pageSize,
      totalCount,
      totalPage: Math.ceil(totalCount / pageSize),
      sortField,
      sortOrder,
      list: contentsFavoritesList.map((v) => {
        const obj = v.toJSON();
        return {
          ...obj,
          isLiked: likedSet.has(obj.id),
          isFavorited: favoritedSet.has(obj.id),
        };
      }),
    };
  }
}
