{"name": "dimensionalc-orridor-sever", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build && copyfiles -u 1 \"src/**/*.hbs\" \"src/**/*.txt\" \"src/public/*\" dist", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "pnpm build && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@alicloud/dysmsapi20170525": "^4.1.0", "@alicloud/openapi-client": "^0.4.14", "@alicloud/tea-util": "^1.4.10", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.6", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.1.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "config": "^3.3.12", "cookie-parser": "^1.4.7", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "helmet": "^8.1.0", "mongoose": "8.13.2", "mongoose-autopopulate": "^1.1.0", "passport": "^0.7.0", "passport-wechat": "^2.0.4", "redis": "^5.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "svg-captcha": "^1.4.0", "swagger-ui-express": "^5.0.1", "twilio": "^5.5.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.22", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.15.2", "@types/supertest": "^6.0.3", "@types/config": "^3.3.5", "@types/cookie-parser": "^1.4.8", "copyfiles": "^2.4.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}