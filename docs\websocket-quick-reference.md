# WebSocket 快速参考

## 🚀 快速开始

### 1. 基础连接

```javascript
import { io } from 'socket.io-client';

const socket = io('ws://localhost:3000/ws', {
  auth: { token: 'Bearer your-jwt-token' }
});
```

### 2. 事件监听

```javascript
// 连接状态
socket.on('connect', () => console.log('已连接'));
socket.on('disconnect', () => console.log('已断开'));

// 消息监听
socket.on('private_message', (data) => {
  console.log(`${data.from}: ${data.content}`);
});

socket.on('group_message', (data) => {
  console.log(`[${data.groupId}] ${data.from}: ${data.content}`);
});

socket.on('system_notice', (data) => {
  console.log(`系统通知: ${data.content}`);
});
```

### 3. 发送消息

```javascript
// 私聊
socket.emit('private_message', {
  receiverId: 'user123',
  content: '你好！'
});

// 群聊
socket.emit('group_message', {
  groupId: 'group456',
  content: '大家好！'
});

// 加入群组
socket.emit('join_group', 'group456');

// 离开群组
socket.emit('leave_group', 'group456');
```

## 📋 事件列表

### 客户端发送事件

| 事件名 | 数据格式 | 说明 |
|--------|----------|------|
| `private_message` | `{receiverId, content}` | 发送私聊消息 |
| `group_message` | `{groupId, content}` | 发送群聊消息 |
| `system_notice` | `{type, content}` | 发送系统通知(管理员) |
| `join_group` | `groupId` | 加入群组 |
| `leave_group` | `groupId` | 离开群组 |

### 服务端推送事件

| 事件名 | 数据格式 | 说明 |
|--------|----------|------|
| `private_message` | `{from, content, timestamp}` | 接收私聊消息 |
| `group_message` | `{groupId, from, content, timestamp}` | 接收群聊消息 |
| `system_notice` | `{type, content, timestamp}` | 接收系统通知 |
| `group_status` | `{groupId, action, members}` | 群组状态变化 |
| `global_notice` | `{...}` | 全局广播 |

## 🔧 常用代码片段

### React Hook

```javascript
import { useEffect, useState } from 'react';
import { io } from 'socket.io-client';

export const useWebSocket = (token) => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!token) return;

    const newSocket = io('ws://localhost:3000/ws', {
      auth: { token: `Bearer ${token}` }
    });

    newSocket.on('connect', () => setIsConnected(true));
    newSocket.on('disconnect', () => setIsConnected(false));

    setSocket(newSocket);
    return () => newSocket.close();
  }, [token]);

  return { socket, isConnected };
};
```

### 消息发送函数

```javascript
const sendMessage = (socket, type, data) => {
  if (socket && socket.connected) {
    socket.emit(type, data);
  } else {
    console.error('Socket未连接');
  }
};

// 使用示例
sendMessage(socket, 'private_message', {
  receiverId: 'user123',
  content: '你好！'
});
```

### 错误处理

```javascript
socket.on('connect_error', (error) => {
  console.error('连接失败:', error.message);
  if (error.message === '用户未登录') {
    // 重新登录
    window.location.href = '/login';
  }
});

socket.on('error', (error) => {
  console.error('操作失败:', error.message);
});
```

### 重连机制

```javascript
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;

socket.on('disconnect', (reason) => {
  if (reason === 'io server disconnect' && reconnectAttempts < maxReconnectAttempts) {
    setTimeout(() => {
      reconnectAttempts++;
      socket.connect();
    }, 1000 * reconnectAttempts);
  }
});

socket.on('connect', () => {
  reconnectAttempts = 0;
});
```

## 🛠️ 调试技巧

```javascript
// 开启调试模式
localStorage.debug = 'socket.io-client:socket';

// 监听所有事件
socket.onAny((event, ...args) => {
  console.log(`事件: ${event}`, args);
});

// 检查连接状态
console.log('连接状态:', socket.connected);
console.log('Socket ID:', socket.id);
```

## ⚠️ 注意事项

1. **认证**: 连接时必须提供有效的JWT token
2. **权限**: 系统通知只有管理员可以发送
3. **群组**: 发送群聊消息前需要先加入群组
4. **重连**: 实现自动重连机制以提高用户体验
5. **错误**: 监听错误事件并进行适当处理

## 🔗 相关文档

- [完整使用文档](./websocket-usage.md)
- [Socket.IO 官方文档](https://socket.io/docs/)
