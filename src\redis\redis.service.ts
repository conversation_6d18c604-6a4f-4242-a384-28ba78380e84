// redis.service.ts
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { RedisClientType } from 'redis';
import { InjectDataRedis, InjectPubSubRedis } from './redis.decorators';

@Injectable()
export class RedisService implements OnModuleDestroy {
  constructor(
    @InjectDataRedis() private readonly dataClient: RedisClientType,
    @InjectPubSubRedis() private readonly pubsubClient: RedisClientType
  ) {}

  // ================= 生命周期管理 =================
  async onModuleDestroy() {
    await Promise.allSettled([
      this.dataClient.quit(),
      this.pubsubClient.quit(),
    ]);
  }

  // ================= 基础操作 =================
  async set(
    key: string,
    value: string | number | Buffer,
    ttl?: number
  ): Promise<void> {
    const options = ttl ? { EX: ttl } : undefined;
    await this.dataClient.set(key, value, options);
  }

  async get(key: string): Promise<string | null> {
    return this.dataClient.get(key);
  }

  async del(key: string): Promise<number> {
    return this.dataClient.del(key);
  }

  async keys(key: string): Promise<string[]> {
    return this.dataClient.keys(key);
  }

  async exists(key: string): Promise<boolean> {
    const result = await this.dataClient.exists(key);
    return result === 1;
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    return (await this.dataClient.expire(key, seconds)) === 1;
  }

  async ttl(key: string): Promise<number> {
    return this.dataClient.ttl(key);
  }

  // ================= 哈希表操作 =================
  async hSet(key: string, field: string, value: string): Promise<number> {
    return this.dataClient.hSet(key, field, value);
  }

  async hGet(key: string, field: string): Promise<string | null> {
    return this.dataClient.hGet(key, field);
  }

  // ================= 列表操作 =================
  async lPush(key: string, ...values: string[]): Promise<number> {
    return this.dataClient.lPush(key, values);
  }

  async rPush(key: string, ...values: string[]): Promise<number> {
    return this.dataClient.rPush(key, values);
  }

  async lRange(key: string, start: number, end: number): Promise<string[]> {
    return this.dataClient.lRange(key, start, end);
  }

  // ================= 集合操作 =================
  async sAdd(key: string, ...members: string[]): Promise<number> {
    return this.dataClient.sAdd(key, members);
  }

  async sMembers(key: string): Promise<string[]> {
    return this.dataClient.sMembers(key);
  }

  async sRem(key: string, ...members: string[]): Promise<number> {
    return this.dataClient.sRem(key, members);
  }

  // ================= 发布订阅操作 =================
  async subscribe(
    channel: string,
    callback: (message: string) => void
  ): Promise<void> {
    await this.pubsubClient.subscribe(channel, (message) => callback(message));
  }

  async unsubscribe(channel: string): Promise<void> {
    await this.pubsubClient.unsubscribe(channel);
  }

  async publish(channel: string, message: string): Promise<number> {
    return this.dataClient.publish(channel, message);
  }

  // ================= 高级操作 =================

  async flushDb(): Promise<boolean> {
    return (await this.dataClient.flushDb()) === 'OK';
  }

  async ping(): Promise<string> {
    return this.dataClient.ping();
  }

  // ================= 客户端实例获取 =================
  getDataClient(): RedisClientType {
    return this.dataClient;
  }

  getPubSubClient(): RedisClientType {
    return this.pubsubClient;
  }
}
