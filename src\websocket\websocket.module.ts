import { Module } from '@nestjs/common'; // 导入 NestJS 的 Module 装饰器
import { WebsocketGateway } from './websocket.gateway'; // 导入 WebsocketGateway 类
import { RedisModule } from '../redis/redis.module'; // 导入 RedisModule 模块
import { WsAuthGuard } from './ws-auth.guard';

// 定义 WebSocket 模块
@Module({
  imports: [RedisModule], // 导入 Redis 模块，以便在 WebsocketGateway 中使用 Redis 服务
  providers: [WebsocketGateway, WsAuthGuard], // 提供 WebsocketGateway 类，用于依赖注入
  exports: [WebsocketGateway, WsAuthGuard], // 导出 WebsocketGateway 类，以便其他模块可以使用
})
export class WebsocketModule {} // 导出 WebsocketModule 类
